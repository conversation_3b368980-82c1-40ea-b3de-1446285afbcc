<template>
  <view class="login-container">
    <!-- 背景装饰 -->
    <view class="background-decoration">
      <view class="decoration-circle circle-1"></view>
      <view class="decoration-circle circle-2"></view>
      <view class="decoration-circle circle-3"></view>
    </view>

    <!-- 主要内容区域 -->
    <view class="login-content">
      <!-- Logo和标题区域 -->
      <view class="header-section">
        <view class="logo-container">
          <image class="logo-image" mode="aspectFill" src="/static/logo.png" />
          <view class="logo-shadow"></view>
        </view>
        <text class="app-title">数据支持系统</text>
        <text class="app-subtitle">Data Support System</text>
      </view>

      <!-- 登录表单区域 -->
      <view class="form-container">
        <view class="form-card">
          <!-- 用户名输入框 -->
          <view class="input-group" :class="{ 'input-focused': usernameFocused }">
            <view class="input-icon">
              <wd-icon name="user" size="20px" color="#666" />
            </view>
            <input class="form-input" type="text" v-model="formState.username" placeholder="请输入账号" @focus="usernameFocused = true" @blur="usernameFocused = false" />
          </view>

          <!-- 密码输入框 -->
          <view class="input-group" :class="{ 'input-focused': passwordFocused }">
            <view class="input-icon">
              <wd-icon name="lock-on" size="20px" color="#666" />
            </view>
            <input class="form-input" type="password" v-model="formState.password" placeholder="请输入密码" @focus="passwordFocused = true" @blur="passwordFocused = false" />
          </view>

          <!-- 登录按钮组 -->
          <view class="button-group">
            <view class="login-button primary-button" :class="{ 'button-disabled': !is, 'button-active': is }" @click="submit" v-if="is"> 登录 </view>
            <view class="login-button disabled-button" v-else> 登录 </view>
            <view class="login-button secondary-button" @click="faceVerification">
              <wd-icon name="camera" size="18px" color="#fff" style="margin-right: 8rpx" />
              人脸识别登录
            </view>
          </view>
        </view>
      </view>
    </view>

    <Lod v-if="show" />
  </view>
  <wd-toast />
</template>

<script setup>
import { ref, computed } from 'vue'
import { login, loginFace, getUserInfo } from '../../services/model/login.js'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { activeRecord } from '/src/services/model/activer.user.record.js'
import { useCommonStore } from '/src/store/common.js'

import { storeToRefs } from 'pinia'
import Lod from './lod.vue'
import uniUtil from '/src/utils/uniUtil'
import { cache } from '/src/utils/cache'
import { useToast } from 'wot-design-uni'

// #ifdef APP-PLUS
import face from '@/uni_modules/mcc-face/index.js'
// #endif

const { equipmentInformation } = storeToRefs(useCommonStore())

const toast = useToast()

const type = ref(null)
onLoad((e) => {
  type.value = e.type
})

const isLoginToken = ref(false)
onShow(() => {
  isLoginToken.value = cache.get('userInfo') ? true : false
})

const formState = ref({ username: null, password: null })
const userState = uniUtil.get('userState')
if (userState) formState.value = userState

// 输入框焦点状态
const usernameFocused = ref(false)
const passwordFocused = ref(false)

const is = computed(() => formState.value.username && formState.value.password)

// 提交登录
const submit = async () => {
  try {
    const { token, data } = await login(formState.value)
    const [userInfo] = JSON.parse(data)
    userInfo.token = token

    if (userInfo) {
      uniUtil.set('userInfo', userInfo)
      uniUtil.set('userState', formState.value)
    }
    if (isLoginToken.value) {
      let stepNumber = 0
      const pages = getCurrentPages().reverse()
      for (let i = 0; i < pages.length; i++) {
        const page = pages[i]
        if (page.route === 'src/pages/login/index') {
          stepNumber++
        } else {
          // 结束循环
          break
        }
      }
      uni.navigateBack({ delta: stepNumber })
    } else {
      uniUtil.reLaunch('../../pages/home/<USER>')
    }
    // #ifdef APP-PLUS
    recordUser()
    // #endif
  } catch (e) {
    toast.error('登录失败，请检查账号密码是否正确')
    console.error(e)
  }
}

const show = ref(false)
async function faceVerification() {
  // #ifdef APP-PLUS
  try {
    face.open(['b'], getimgCallBack)
  } catch (error) {
    uniUtil.showToast(error)
  }
  async function getimgCallBack(base64) {
    face.close()
    show.value = true
    const { token } = await loginFace({
      base64Image: base64.replace('data:image/png;base64,', '')
    })
    const { code, data } = await getUserInfo(token)
    if (code === 200) {
      const [userInfo] = JSON.parse(data)
      userInfo.token = token
      if (userInfo) uniUtil.set('userInfo', userInfo)
      if (isLoginToken.value) {
        let stepNumber = 0
        const pages = getCurrentPages().reverse()
        for (let i = 0; i < pages.length; i++) {
          const page = pages[i]
          if (page.route === 'src/pages/login/index') {
            stepNumber++
          } else {
            // 结束循环
            break
          }
        }
        uni.navigateBack({ delta: stepNumber })
      } else {
        uniUtil.reLaunch('../../pages/home/<USER>')
      }

      // #ifdef APP-PLUS
      recordUser()
      // #endif
    } else {
      show.value = false
      uniUtil.showToast('人脸识别失败,请重试！')
    }
  }
  // #endif
}

async function recordUser() {
  const userInfo = uniUtil.get('userInfo')
  if (userInfo && userInfo.name !== '开发账号') {
    await activeRecord({ userName: userInfo.name, ...equipmentInformation.value })
  }
}
</script>

<style lang="less" scoped>
/* 登录容器 */
.login-container {
  position: relative;
  width: 100%;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.circle-1 {
  width: 300rpx;
  height: 300rpx;
  top: -150rpx;
  right: -150rpx;
  animation: float 6s ease-in-out infinite;
}

.circle-2 {
  width: 200rpx;
  height: 200rpx;
  bottom: 200rpx;
  left: -100rpx;
  animation: float 8s ease-in-out infinite reverse;
}

.circle-3 {
  width: 150rpx;
  height: 150rpx;
  top: 30%;
  right: 50rpx;
  animation: float 10s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-30px) rotate(180deg);
  }
}

/* 主要内容区域 */
.login-content {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 60rpx 40rpx;
}

/* 头部区域 */
.header-section {
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-container {
  position: relative;
  display: inline-block;
  margin-bottom: 40rpx;
}

.logo-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

.logo-shadow {
  position: absolute;
  top: 10rpx;
  left: 10rpx;
  width: 200rpx;
  height: 200rpx;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  z-index: 1;
}

.app-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 16rpx;
  text-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 300;
  letter-spacing: 2rpx;
}

/* 表单容器 */
.form-container {
  width: 100%;
  max-width: 600rpx;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 输入框组 */
.input-group {
  position: relative;
  margin-bottom: 32rpx;
  background: #f8f9fa;
  border-radius: 20rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  overflow: hidden;
}

.input-group.input-focused {
  border-color: #667eea;
  background: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);
  transform: translateY(-2rpx);
}

.input-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  z-index: 2;
}

.form-input {
  width: 100%;
  height: 88rpx;
  padding: 0 24rpx 0 72rpx;
  font-size: 32rpx;
  color: #333333;
  background: transparent;
  border: none;
  outline: none;
}

.form-input::placeholder {
  color: #999999;
  font-size: 30rpx;
}

.forgot-link {
  position: absolute;
  right: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 28rpx;
  color: #667eea;
  cursor: pointer;
}

/* 按钮组 */
.button-group {
  margin-top: 40rpx;
}

.login-button {
  width: 100%;
  height: 88rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-button:last-child {
  margin-bottom: 0;
}

.primary-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
}

.primary-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.disabled-button {
  background: #e9ecef;
  color: #6c757d;
  cursor: not-allowed;
}

.secondary-button {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: #ffffff;
  box-shadow: 0 8rpx 24rpx rgba(79, 172, 254, 0.3);
}

.secondary-button:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(79, 172, 254, 0.3);
}

/* 按钮波纹效果 */
.login-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.6s, height 0.6s, top 0.6s, left 0.6s;
  transform: translate(-50%, -50%);
}

.login-button:active::before {
  width: 300rpx;
  height: 300rpx;
}
</style>
