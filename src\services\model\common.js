import { nodeRequest } from '../index'

class Common {
  queryDictionaries(type) {
    return nodeRequest.get(`/nodeServer/dictionaries/${type}`)
  }

  coordinateTransformation(data) {
    return nodeRequest.post('/nodeServer/resource/coordinateTransformation', data)
  }

  verifyToken() {
    return nodeRequest.get(`/nodeServer/common/verify`)
  }

  compare(version) {
    return nodeRequest.get(`/nodeServer/versionControl/compare?version=${version}`)
  }

  // 保存Cid
  async saveCid(cid) {
    return nodeRequest.get(`/nodeServer/pushMessage/check?cid=${cid}`)
  }

  // 查询推送消息列表
  messageList() {
    return nodeRequest.get(`/nodeServer/pushMessage/deliveryList`)
  }

  ossVoucher(params) {
    // params = { privately: 0 | 1 , bucket: 'bucketName' }
    return nodeRequest.get(`/nodeServer/ossServe/voucher`, { params })
  }
}

export const CommonApi = new Common()
