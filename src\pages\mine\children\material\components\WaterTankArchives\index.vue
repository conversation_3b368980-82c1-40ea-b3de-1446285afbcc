<template>
  <div class="waterTankArchives">
    <div class="info-card water-tank-card">
      <div class="card-header relative" v-if="data">
        <div class="header-icon">
          <wd-icon name="tools" size="18px" color="#13c2c2" />
        </div>
        <div class="header-title">水箱档案</div>
        <div class="flex absolute" style="top: 0; right: 0">
          <div class="header-edit" v-if="isUapdata">
            <wd-icon name="add" size="18px" @click="handleAddTank" color="#13c2c2" />
          </div>

          <div class="header-edit" @click="handleToggleEdit" v-if="showUpdata && !ban">
            <wd-icon :name="`${!isUapdata ? 'edit-1' : 'close'}`" size="18px" :color="`${!isUapdata ? '#13c2c2' : '#fe3b0d'}`" />
          </div>

          <div class="header-edit" v-if="isUapdata">
            <wd-icon name="check" size="18px" @click="handleSaveArchive" color="#1792e1" />
          </div>
        </div>
        <div class="header-extra">
          <span class="extra-count">共 {{ menuData.total_count || (menuData.T && menuData.T.length) || 0 }} 个</span>
          <span class="extra-chip">小区类别：{{ menuData.area_category || '-' }}</span>
          <span class="extra-chip">系统编码：{{ menuData.system_code || '-' }}</span>
          <div style="width: 16rpx"></div>
        </div>
      </div>

      <div class="info-grid" v-if="data">
        <div class="info-item tank-item" v-for="(tank, index) in displayData" :key="index" v-if="displayData.length">
          <div class="item-icon tank-icon">
            <wd-icon name="tools" size="14px" color="#13c2c2" />
          </div>
          <div class="item-content">
            <div class="item-label">水箱 #{{ index + 1 }}</div>
            <!-- 编辑模式 - 表单 -->
            <div v-if="isUapdata" class="tank-form">
              <div class="form-header">
                <div class="form-item">
                  <label class="form-label">位置</label>
                  <input :cursor-spacing="125" v-model="tank.location" :class="['form-input', { 'form-input-empty': !tank.location }]" placeholder="请输入位置" />
                </div>
                <div class="form-actions">
                  <button class="delete-btn" @click="handleDeleteTank(index)" v-if="displayData.length">
                    <wd-icon name="delete" size="14px" color="#fe3b0d" />
                  </button>
                </div>
              </div>
              <div class="form-details">
                <div class="form-item">
                  <label class="form-label">容量</label>
                  <input type="number" :cursor-spacing="70" v-model="tank.capacity" :class="['form-input', { 'form-input-empty': !tank.capacity }]" placeholder="请输入容量" />
                </div>
                <div class="form-item">
                  <label class="form-label">数量</label>
                  <input type="number" :cursor-spacing="70" v-model="tank.quantity" :class="['form-input', { 'form-input-empty': !tank.quantity }]" placeholder="请输入数量" />
                </div>
                <div class="form-item">
                  <label class="form-label">类型</label>
                  <input :cursor-spacing="25" v-model="tank.type" :class="['form-input', { 'form-input-empty': !tank.type }]" placeholder="请输入类型" />
                </div>
                <div class="form-item">
                  <label class="form-label">材质</label>
                  <input :cursor-spacing="25" v-model="tank.material" :class="['form-input', { 'form-input-empty': !tank.material }]" placeholder="请输入材质" />
                </div>
              </div>
            </div>

            <!-- 显示模式 - 原有样式 -->
            <div v-else>
              <div class="item-value tank-location">{{ tank.location || '未填写位置' }}</div>
              <div class="tank-details">
                <div class="detail-item">
                  <span class="detail-label">容量</span>
                  <span class="detail-value">{{ tank.capacity || '-' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">数量</span>
                  <span class="detail-value">{{ tank.quantity || '-' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">类型</span>
                  <span class="detail-value">{{ tank.type || '-' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">材质</span>
                  <span class="detail-value">{{ tank.material || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="empty-grid-state">
          <div class="empty-icon">
            <wd-icon name="plus-circle" size="32px" color="#13c2c2" />
          </div>
          <div class="empty-text">暂无水箱档案数据</div>
        </div>
      </div>
      <div v-if="!data" class="no-data small">
        <div class="no-data-illustration">�</div>
        <div class="no-data-title">暂无水箱档案</div>
        <div class="no-data-description">还没有创建任何水箱档案信息</div>
        <div class="no-data-action">
          <button class="create-archive-btn" v-if="showUpdata" @click="handleCreateArchive">
            <div class="btn-icon">
              <wd-icon name="plus" size="16px" color="#fff" />
            </div>
            <span class="btn-text">创建档案</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { createPoolTank, updatePoolTank } from '/src/services/model/zone.record'
import { useToast, useMessage } from 'wot-design-uni'
import { cache } from '/src/utils/cache.js'

const emit = defineEmits(['created-success'])
const props = defineProps({ data: { type: Object }, ban: { type: Boolean, default: false }, detail: { type: Object } })
const toast = useToast()
const message = useMessage()

const isUapdata = ref(false)
const editableData = ref([])
const userInfo = cache.get('userInfo')
const safeSplit = (val) => (val ?? '').toString().split(';')

const menuData = computed(() => {
  const d = props.data
  if (!d) return {}
  const { total_count, area_category, system_code } = d
  const locations = safeSplit(d.location)
  const capacities = safeSplit(d.capacity)
  const types = safeSplit(d.type)
  const materials = safeSplit(d.material)
  const quantities = safeSplit(d.quantity)
  const T = locations
    .map((item, index) => {
      if (!item) return null
      return {
        location: item || '-',
        capacity: capacities[index] || '-',
        type: types[index] || '-',
        material: materials[index] || '-',
        quantity: quantities[index] || '-'
      }
    })
    .filter(Boolean)
  return { T, total_count, area_category, system_code }
})

const showUpdata = computed(() => {
  return ['code', 'qianwensong', 'penggongxi', 'jiangyiyan', 'admin', 'zouliangkun'].includes(userInfo?.Username)
})

// 显示数据 - 根据编辑状态决定使用原始数据还是可编辑数据
const displayData = computed(() => {
  if (isUapdata.value) {
    return editableData.value
  }
  return menuData.value.T || []
})

// 监听编辑状态变化，初始化可编辑数据
watch(
  isUapdata,
  (newVal) => {
    if (newVal) {
      // 进入编辑模式，复制原始数据到可编辑数据
      editableData.value = JSON.parse(JSON.stringify(menuData.value.T || []))
    }
  },
  { immediate: false }
)

// 监听原始数据变化，更新可编辑数据
watch(
  () => menuData.value.T,
  (newVal) => {
    if (!isUapdata.value && newVal) {
      editableData.value = JSON.parse(JSON.stringify(newVal))
    }
  },
  { immediate: true, deep: true }
)

// 切换编辑模式
function handleToggleEdit() {
  isUapdata.value = !isUapdata.value
}

// 添加新水箱
function handleAddTank() {
  editableData.value.unshift({ location: '', capacity: '', type: '', material: '', quantity: '' })
}

// 删除水箱
function handleDeleteTank(index) {
  if (editableData.value.length) {
    editableData.value.splice(index, 1)
  }
}

// 保存档案修改
function handleSaveArchive() {
  message
    .confirm({
      msg: '是否保存修改？确定将覆盖原数据。 ',
      title: '保存修改'
    })
    .then(() => {
      const eveny = editableData.value.every((item) => Object.values(item).every(Boolean))
      if (!eveny) return toast.warning('水池箱数据未填写完整')
      // 这里可以添加保存逻辑
      const params = {
        location: editableData.value
          .map((item) => item.location)
          .filter(Boolean)
          .join(';'),
        capacity: editableData.value
          .map((item) => item.capacity)
          .filter(Boolean)
          .join(';'),
        type: editableData.value
          .map((item) => item.type)
          .filter(Boolean)
          .join(';'),
        material: editableData.value
          .map((item) => item.material)
          .filter(Boolean)
          .join(';'),
        quantity: editableData.value
          .map((item) => item.quantity)
          .filter(Boolean)
          .join(';'),
        xqbm: props.detail.xqbm
      }

      return updatePoolTank(params)
    })
    .then((res) => {
      emit('created-success')
      isUapdata.value = false
      toast.success('保存成功')
    })
    .catch((error) => {
      toast.error('保存失败')
    })
}

// 创建水池箱档案
async function handleCreateArchive() {
  try {
    await createPoolTank({
      standard_area_name: props.detail.xqmc,
      area_category: props.detail.xqlb.split('-')[0],
      water_office: props.detail.sws,
      street_office: props.detail.ssjd + '办',
      pool_area_name: props.detail.xqmc,
      xqbm: props.detail.xqbm
    })
    toast.success('创建成功')
    emit('created-success')
  } catch (error) {
    toast.error('创建失败')
  }
}
</script>

<style lang="less" scoped>
.waterTankArchives {
  padding: 20rpx;

  .info-card.water-tank-card {
    background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
    border-radius: 24rpx;
    padding: 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12), 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
    margin-bottom: 20rpx;
    border: 1rpx solid rgba(19, 194, 194, 0.08);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4rpx;
      background: linear-gradient(90deg, #13c2c2, #36d1dc, #5b86e5);
    }
  }

  .card-header {
    display: flex;
    align-items: center;
    gap: 16rpx;
    margin-bottom: 28rpx;
    flex-wrap: wrap;

    .header-icon {
      width: 48rpx;
      height: 48rpx;
      border-radius: 12rpx;
      background: linear-gradient(135deg, rgba(19, 194, 194, 0.15), rgba(19, 194, 194, 0.25));
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4rpx 16rpx rgba(19, 194, 194, 0.2);
    }
    .header-title {
      font-size: 32rpx;
      font-weight: 700;
      color: #1a1a1a;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
    }

    .header-edit {
      background: linear-gradient(135deg, rgba(19, 194, 194, 0.15), rgba(19, 194, 194, 0.25));
      border-radius: 20%;
      padding: 6rpx 8rpx;
      margin-left: 24rpx;
    }
    .header-extra {
      margin-left: auto;
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx 16rpx;
      align-items: center;
      font-size: 24rpx;
      color: #666;
      line-height: 1.2;

      .extra-count {
        background: linear-gradient(135deg, #e6fffa, #ccfbf1);
        color: #0f766e;
        padding: 6rpx 12rpx;
        border-radius: 16rpx;
        border: 1rpx solid rgba(19, 194, 194, 0.2);

        strong {
          color: #13c2c2;
          font-weight: 700;
        }
      }
      .extra-chip {
        background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
        color: #0369a1;
        padding: 6rpx 12rpx;
        border-radius: 16rpx;
        font-size: 22rpx;
        border: 1rpx solid rgba(3, 105, 161, 0.1);
        box-shadow: 0 2rpx 4rpx rgba(3, 105, 161, 0.1);
      }
    }
  }

  // 信息网格 - 增强视觉效果
  .info-grid {
    // display: grid;
    // grid-template-columns: repeat(auto-fit, minmax(320rpx, 1fr));
    // gap: 20rpx;
  }

  // 网格内空状态
  .empty-grid-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40rpx 20rpx;
    background: linear-gradient(135deg, rgba(19, 194, 194, 0.02), rgba(54, 209, 220, 0.01));
    border-radius: 16rpx;
    border: 2rpx dashed rgba(19, 194, 194, 0.15);
    margin: 20rpx 0;

    .empty-icon {
      margin-bottom: 16rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 24rpx;
      color: #999;
      margin-bottom: 20rpx;
      font-weight: 500;
    }

    .create-btn-inline {
      display: inline-flex;
      align-items: center;
      gap: 6rpx;
      background: linear-gradient(135deg, #13c2c2, #36d1dc);
      color: #fff;
      border: none;
      border-radius: 20rpx;
      padding: 8rpx 16rpx;
      font-size: 22rpx;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2rpx 8rpx rgba(19, 194, 194, 0.2);

      &:hover {
        transform: translateY(-1rpx) scale(1.02);
        box-shadow: 0 4rpx 12rpx rgba(19, 194, 194, 0.3);
        background: linear-gradient(135deg, #0ea5a5, #2dd4aa);
      }

      &:active {
        transform: translateY(0) scale(1);
        box-shadow: 0 1rpx 4rpx rgba(19, 194, 194, 0.2);
      }
    }
  }

  .info-item.tank-item {
    display: flex;
    align-items: flex-start;
    background: linear-gradient(135deg, rgba(19, 194, 194, 0.03), rgba(54, 209, 220, 0.02));
    border-radius: 16rpx;
    padding: 20rpx;
    margin-bottom: 16rpx;
    border: 2rpx solid rgba(19, 194, 194, 0.12);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4rpx;
      height: 100%;
      background: linear-gradient(180deg, #13c2c2, #36d1dc);
      opacity: 0.6;
    }

    &:hover {
      background: linear-gradient(135deg, rgba(19, 194, 194, 0.08), rgba(54, 209, 220, 0.05));
      border-color: rgba(19, 194, 194, 0.25);
      transform: translateY(-4rpx) scale(1.02);
      box-shadow: 0 12rpx 40rpx rgba(19, 194, 194, 0.15), 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    }
  }

  .item-icon.tank-icon {
    width: 40rpx;
    height: 40rpx;
    border-radius: 12rpx;
    background: linear-gradient(135deg, rgba(19, 194, 194, 0.15), rgba(19, 194, 194, 0.25));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
    flex-shrink: 0;
    box-shadow: 0 4rpx 12rpx rgba(19, 194, 194, 0.2);
    border: 1rpx solid rgba(19, 194, 194, 0.2);
  }

  .item-content {
    flex: 1;
  }

  .item-label {
    font-size: 24rpx;
    color: #13c2c2;
    line-height: 1.2;
    margin-bottom: 6rpx;
    font-weight: 600;
  }

  .item-value.tank-location {
    font-size: 30rpx;
    font-weight: 700;
    color: #1a1a1a;
    line-height: 1.3;
    margin-bottom: 16rpx;
    text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.05);
  }

  .tank-details {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12rpx 20rpx;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 12rpx;
    padding: 16rpx;
    border: 1rpx solid rgba(19, 194, 194, 0.08);
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8rpx 12rpx;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(248, 250, 252, 0.9));
    border-radius: 8rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, rgba(19, 194, 194, 0.05), rgba(54, 209, 220, 0.03));
      border-color: rgba(19, 194, 194, 0.15);
      transform: translateY(-1rpx);
      box-shadow: 0 4rpx 8rpx rgba(19, 194, 194, 0.1);
    }
  }

  .detail-label {
    font-size: 22rpx;
    color: #666;
    min-width: 80rpx;
    font-weight: 500;
  }

  .detail-value {
    font-size: 24rpx;
    color: #1a1a1a;
    font-weight: 600;
    text-align: right;
    flex: 1;
  }

  // 表单样式
  .tank-form {
    width: 100%;

    .form-header {
      display: flex;
      align-items: flex-end;
      gap: 16rpx;
      margin-bottom: 16rpx;

      .form-item {
        flex: 1;
        margin-bottom: 0;
      }

      .form-actions {
        display: flex;
        align-items: center;
        gap: 8rpx;

        .delete-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32rpx;
          height: 48rpx;
          border: none;
          border-radius: 6rpx;
          background: rgba(254, 59, 13, 0.1);
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(254, 59, 13, 0.2);
            transform: scale(1.1);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }

    .form-item {
      // margin-bottom: 16rpx;

      .form-label {
        display: block;
        font-size: 22rpx;
        color: #666;
        margin-bottom: 8rpx;
        font-weight: 500;
      }

      .form-input {
        width: 100%;
        height: 50rpx;
        padding-left: 12rpx;
        border: 1rpx solid rgba(19, 194, 194, 0.2);
        border-radius: 8rpx;
        font-size: 24rpx;
        color: #1a1a1a;
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
        box-sizing: border-box;

        &:focus {
          outline: none;
          border-color: #13c2c2;
          background: #fff;
          box-shadow: 0 0 0 2rpx rgba(19, 194, 194, 0.1);
        }

        &::placeholder {
          color: #999;
          font-size: 22rpx;
        }

        // 空值时显示红色边框
        &:placeholder-shown {
          border-color: #fe3b0d;
          background: rgba(254, 59, 13, 0.05);
        }

        // 空值样式类
        &.form-input-empty {
          border-color: #fe3b0d !important;
          background: rgba(254, 59, 13, 0.05) !important;

          &:focus {
            border-color: #fe3b0d !important;
            box-shadow: 0 0 0 2rpx rgba(254, 59, 13, 0.1) !important;
          }
        }
      }
    }

    .form-details {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16rpx;
      margin-top: 16rpx;
      padding: 16rpx;
      background: rgba(255, 255, 255, 0.6);
      border-radius: 12rpx;
      border: 1rpx solid rgba(19, 194, 194, 0.08);
    }
  }

  // 空状态
  .no-data.small {
    background: linear-gradient(135deg, #fff 0%, #fafbfc 100%);
    border-radius: 16rpx;
    padding: 40rpx 24rpx;
    text-align: center;
    color: #999;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08), 0 1rpx 4rpx rgba(0, 0, 0, 0.04);
    border: 2rpx dashed rgba(19, 194, 194, 0.2);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 80rpx;
      height: 80rpx;
      background: radial-gradient(circle, rgba(19, 194, 194, 0.08) 0%, transparent 70%);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      z-index: 0;
    }

    .no-data-illustration {
      font-size: 48rpx;
      margin-bottom: 12rpx;
      opacity: 0.7;
      position: relative;
      z-index: 1;
      filter: drop-shadow(0 1rpx 2rpx rgba(19, 194, 194, 0.2));
    }
    .no-data-title {
      font-size: 26rpx;
      color: #666;
      font-weight: 500;
      position: relative;
      z-index: 1;
      margin-bottom: 6rpx;
    }

    .no-data-description {
      font-size: 22rpx;
      color: #999;
      position: relative;
      z-index: 1;
      margin-bottom: 20rpx;
      line-height: 1.3;
    }

    .no-data-action {
      position: relative;
      z-index: 1;
    }

    .create-archive-btn {
      display: inline-flex;
      align-items: center;
      gap: 6rpx;
      background: linear-gradient(135deg, #13c2c2, #36d1dc);
      color: #fff;
      border: none;
      border-radius: 24rpx;
      padding: 12rpx 24rpx;
      font-size: 24rpx;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      box-shadow: 0 4rpx 12rpx rgba(19, 194, 194, 0.25), 0 1rpx 4rpx rgba(19, 194, 194, 0.15);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s ease;
      }

      &:hover {
        transform: translateY(-1rpx) scale(1.03);
        box-shadow: 0 6rpx 20rpx rgba(19, 194, 194, 0.35), 0 2rpx 8rpx rgba(19, 194, 194, 0.2);
        background: linear-gradient(135deg, #0ea5a5, #2dd4aa);

        &::before {
          left: 100%;
        }
      }

      &:active {
        transform: translateY(0) scale(1.01);
        box-shadow: 0 3rpx 8rpx rgba(19, 194, 194, 0.25), 0 1rpx 4rpx rgba(19, 194, 194, 0.15);
      }

      .btn-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 20rpx;
        height: 20rpx;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4rpx);
      }

      .btn-text {
        position: relative;
        z-index: 1;
      }
    }
  }
}
.updata-content {
  padding: 56rpx 24rpx 24rpx 24rpx;
  height: 100%;
  box-sizing: border-box;
}
@media (max-width: 768px) {
  .waterTankArchives {
    padding: 16rpx;
  }
  .info-grid {
    grid-template-columns: 1fr;
  }
  .tank-details {
    grid-template-columns: 1fr;
  }
}
</style>

<style lang="less">
.u1-popup {
  .wd-popup__close {
    color: #444444 !important;
  }
}
</style>
