<template>
  <div class="second-source-container">
    <template v-for="(item, index) in list" :key="item.id">
      <div @click="handleClick(item)" class="pump-house-card mar-B16" :class="getStatusClass(item.progressStatus)" :style="{ animationDelay: `${index * 0.1}s` }">
        <!-- 清晰结构化布局 -->
        <div class="card-layout">
          <!-- 头部：标题和状态 -->
          <div class="card-header f-between">
            <div class="title-section f-y-center">
              <div class="facility-icon f-xy-center">
                <wd-icon name="building" size="16px" color="#fff"></wd-icon>
              </div>
              <div class="title-content">
                <div class="facility-title fon-S26 fon-W600 color-primary">{{ item.PumpHouseName }}</div>
                <div class="facility-area fon-S20 color-666">{{ item.BelongingArea }}</div>
              </div>
            </div>
            <div class="status-section">
              <div class="status-tag" :class="getStatusTagClass(item.progressStatus)">
                {{ item.ProgressStatus }}
              </div>
              <div class="batch-tag" v-if="item.Batch">
                {{ getBatchText(item.Batch) }}
              </div>
            </div>
          </div>

          <!-- 中部：详细信息 -->
          <div class="card-body">
            <div class="info-grid">
              <div class="info-item" v-if="item.BelongingStreet">
                <wd-icon name="location" size="12px" color="#52c41a"></wd-icon>
                <span class="info-text">{{ item.BelongingStreet }}</span>
              </div>
              <div class="info-item" v-if="item.Gridding">
                <wd-icon name="grid" size="12px" color="#722ed1"></wd-icon>
                <span class="info-text">{{ item.Gridding }}</span>
              </div>
              <div class="info-item" v-if="item.PressurizedHouseholds">
                <wd-icon name="home" size="12px" color="#1890ff"></wd-icon>
                <span class="info-text">{{ item.PressurizedHouseholds }}户</span>
              </div>
              <div class="info-item" v-if="item.CurrentNode">
                <wd-icon name="setting" size="12px" color="#faad14"></wd-icon>
                <span class="info-text">{{ getCurrentNodeText(item.CurrentNode) }}</span>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-section" v-if="item.CurrentNode">
              <div class="progress-bar">
                <div class="progress-fill" :style="{ width: Math.round((item.CurrentNode / 17) * 100) + '%' }"></div>
              </div>
              <span class="progress-text fon-S18 color-666">{{ Math.round((item.CurrentNode / 17) * 100) }}%</span>
            </div>

            <!-- 备注信息 -->
            <div class="remark-section" v-if="item.showRemark || item.Remark">
              <wd-icon name="info" size="12px" color="#1890ff"></wd-icon>
              <span class="remark-text fon-S20 color-666">{{ item.showRemark || item.Remark }}</span>
            </div>

            <!-- 地址信息 -->
            <div class="address-section" v-if="item.ResidentialAddress || item.AccuratePosition">
              <wd-icon name="location-outline" size="12px" color="#52c41a"></wd-icon>
              <span class="address-text fon-S20 color-666">{{ item.ResidentialAddress || item.AccuratePosition }}</span>
            </div>
          </div>

          <!-- 底部：时间和箭头 -->
          <div class="card-footer f-between">
            <div class="time-section f-y-center">
              <wd-icon name="time" size="10px" color="#999"></wd-icon>
              <span class="update-time fon-S18 color-999">{{ item.UpdateTime && item.UpdateTime.slice(0, 10) }}</span>
            </div>
            <div class="arrow-section">
              <wd-icon name="arrow-right" color="#ccc" size="14px"></wd-icon>
            </div>
          </div>
        </div>
      </div>
    </template>

    <!-- 空状态 -->
    <div class="empty-state back-white border-R16 box-shadow" v-if="!list.length">
      <wd-status-tip image="search" tip="无数据" />
    </div>

    <!-- 底部间距 -->
    <div class="bottom-spacing"></div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { userHomeStore } from '/src/store/home.js'
import uniUtil from '/src/utils/uniUtil.js'
import { PumpHouseApi } from '/src/services/model/pump.house.js'

const { deliveryDots } = userHomeStore()

const props = defineProps({ code: String })

watch(() => props.code, getList, { immediate: true })

const list = ref([])
watch(list, (val) => deliveryDots(val, 'second'))

async function getList(zoneCode) {
  try {
    const { data } = await PumpHouseApi.seekScope(zoneCode)
    list.value = data
  } catch (error) {
    list.value = []
  }
}

function handleClick(val) {
  uniUtil.navigateTo(`/src/pages/mine/children/pump-house/pumpHouseMap?PumpRoomNumber=${val.PumpRoomNumber}&rollback=true`)
}

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    停工: 'status-stopped',
    滞后: 'status-delayed',
    正常: 'status-normal'
  }
  return statusMap[status] || 'status-default'
}

// 获取状态标签样式类
function getStatusTagClass(status) {
  const tagMap = {
    停工: 'tag-danger',
    滞后: 'tag-warning',
    正常: 'tag-success'
  }
  return tagMap[status] || 'tag-default'
}

// 获取批次文本
function getBatchText(batch) {
  const batchMap = {
    1: '利源代建',
    2: '查漏补缺',
    3: '需纳改',
    4: '无需纳改',
    5: '应改未改'
  }
  return batchMap[batch] || `批次${batch}`
}

// 获取当前节点文本
function getCurrentNodeText(currentNode) {
  const nodeMap = {
    1: '方案审查',
    2: '施工图审查',
    3: '施工进场',
    4: '临时供水',
    5: '拆除旧设备',
    6: '设备进场验收',
    7: '水池改造',
    8: '清洗消毒',
    9: '设备调试',
    10: '泵房装修',
    11: '通水前检查',
    12: '切换新系统',
    13: '视频安防',
    14: '竣工验收',
    15: '二供平台',
    16: '复验日期',
    17: '完成移交',
    18: '项目完成'
  }
  return nodeMap[currentNode] || `节点${currentNode}`
}
</script>

<style lang="less" scoped>
.second-source-container {
  padding: 0 24rpx;
}

// 泵房卡片样式
.pump-house-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 16rpx;
  padding: 16rpx 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
  overflow: hidden;
  animation: slideInUp 0.6s ease-out both;
  border: 1rpx solid rgba(102, 126, 234, 0.08);
  cursor: pointer;

  &:active {
    transform: translateY(-4rpx) scale(0.98);
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.12);
  }

  // 状态左边框
  &.status-stopped {
    border-left: 6rpx solid #ff4d4f;

    .facility-icon {
      background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
      box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.3);
    }
  }

  &.status-delayed {
    border-left: 6rpx solid #faad14;

    .facility-icon {
      background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
      box-shadow: 0 4rpx 16rpx rgba(250, 173, 20, 0.3);
    }
  }

  &.status-normal {
    border-left: 6rpx solid #52c41a;

    .facility-icon {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      box-shadow: 0 4rpx 16rpx rgba(82, 196, 26, 0.3);
    }
  }

  &.status-default {
    border-left: 6rpx solid #1890ff;

    .facility-icon {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      box-shadow: 0 4rpx 16rpx rgba(24, 144, 255, 0.3);
    }
  }
}

// 清晰结构化布局
.card-layout {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

// 卡片头部
.card-header {
  align-items: flex-start;
}

.title-section {
  flex: 1;
  min-width: 0;
  gap: 10rpx;
}

.facility-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
  box-shadow: 0 2rpx 8rpx rgba(77, 99, 224, 0.2);
  flex-shrink: 0;
}

.title-content {
  flex: 1;
  min-width: 0;
}

.facility-title {
  line-height: 1.2;
  word-break: break-all;
  margin-bottom: 2rpx;
}

.facility-area {
  opacity: 0.8;
  line-height: 1.2;
}

.status-section {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6rpx;
  flex-shrink: 0;
}

// 卡片主体
.card-body {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 6rpx 10rpx;
  background: rgba(0, 0, 0, 0.03);
  border-radius: 8rpx;
  font-size: 20rpx;
  color: #666;
  line-height: 1;
}

.info-text {
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 状态标签样式
.status-tag {
  padding: 4rpx 10rpx;
  border-radius: 12rpx;
  font-size: 18rpx;
  font-weight: 500;
  line-height: 1;
  white-space: nowrap;

  &.tag-danger {
    background: rgba(255, 77, 79, 0.1);
    color: #ff4d4f;
    border: 1rpx solid rgba(255, 77, 79, 0.2);
  }

  &.tag-warning {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
    border: 1rpx solid rgba(250, 173, 20, 0.2);
  }

  &.tag-success {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
    border: 1rpx solid rgba(82, 196, 26, 0.2);
  }

  &.tag-default {
    background: rgba(24, 144, 255, 0.1);
    color: #1890ff;
    border: 1rpx solid rgba(24, 144, 255, 0.2);
  }
}

.batch-tag {
  padding: 3rpx 8rpx;
  border-radius: 10rpx;
  font-size: 16rpx;
  font-weight: 400;
  line-height: 1;
  background: rgba(114, 46, 209, 0.1);
  color: #722ed1;
  border: 1rpx solid rgba(114, 46, 209, 0.2);
  white-space: nowrap;
}

// 进度条区域
.progress-section {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.progress-bar {
  flex: 1;
  height: 6rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #faad14 0%, #ffc53d 100%);
  border-radius: 3rpx;
  transition: width 0.3s ease;
}

.progress-text {
  flex-shrink: 0;
  font-weight: 600;
  min-width: 40rpx;
  text-align: right;
}

// 备注和地址区域
.remark-section,
.address-section {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  line-height: 1.4;
}

.remark-section {
  background: rgba(24, 144, 255, 0.05);
  border: 1rpx solid rgba(24, 144, 255, 0.1);
}

.address-section {
  background: rgba(82, 196, 26, 0.05);
  border: 1rpx solid rgba(82, 196, 26, 0.1);
}

.remark-text,
.address-text {
  flex: 1;
  word-break: break-all;
}

// 卡片底部
.card-footer {
  align-items: center;
  padding-top: 8rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
}

.time-section {
  gap: 6rpx;
}

.update-time {
  opacity: 0.8;
}

.arrow-section {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.pump-house-card:active .arrow-section {
  opacity: 1;
  transform: translateX(4rpx);
}

// 删除不再需要的样式，保持文件简洁

// 空状态
.empty-state {
  padding: 80rpx 40rpx;
  text-align: center;
  margin: 40rpx 24rpx;
  background: linear-gradient(135deg, #ffffff 0%, #fafbfc 100%);
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
}

// 底部间距
.bottom-spacing {
  padding: 80rpx;
}

// 动画效果
@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-4rpx) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .pump-house-card {
    padding: 12rpx 16rpx;
  }

  .card-layout {
    gap: 10rpx;
  }

  .title-section {
    gap: 8rpx;
  }

  .facility-icon {
    width: 28rpx;
    height: 28rpx;
  }

  .facility-title {
    font-size: 24rpx;
  }

  .facility-area {
    font-size: 18rpx;
  }

  .status-section {
    gap: 4rpx;
  }

  .status-tag {
    padding: 3rpx 8rpx;
    font-size: 16rpx;
  }

  .batch-tag {
    padding: 2rpx 6rpx;
    font-size: 14rpx;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 6rpx;
  }

  .info-item {
    padding: 5rpx 8rpx;
    font-size: 18rpx;
  }

  .progress-text {
    font-size: 16rpx;
  }

  .remark-section,
  .address-section {
    padding: 6rpx 10rpx;
  }

  .remark-text,
  .address-text {
    font-size: 18rpx;
  }

  .update-time {
    font-size: 16rpx;
  }
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
  .pump-house-card {
    background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
    border-color: rgba(255, 255, 255, 0.1);

    .facility-title {
      color: #ffffff;
    }

    .facility-area,
    .update-time,
    .progress-text {
      color: rgba(255, 255, 255, 0.7);
    }

    .info-item {
      background: rgba(255, 255, 255, 0.05);
      color: rgba(255, 255, 255, 0.8);
    }

    .status-tag {
      &.tag-danger {
        background: rgba(255, 77, 79, 0.2);
        color: #ff7875;
        border-color: rgba(255, 77, 79, 0.3);
      }

      &.tag-warning {
        background: rgba(250, 173, 20, 0.2);
        color: #ffc53d;
        border-color: rgba(250, 173, 20, 0.3);
      }

      &.tag-success {
        background: rgba(82, 196, 26, 0.2);
        color: #73d13d;
        border-color: rgba(82, 196, 26, 0.3);
      }

      &.tag-default {
        background: rgba(24, 144, 255, 0.2);
        color: #40a9ff;
        border-color: rgba(24, 144, 255, 0.3);
      }
    }

    .batch-tag {
      background: rgba(114, 46, 209, 0.2);
      color: #b37feb;
      border-color: rgba(114, 46, 209, 0.3);
    }

    .progress-bar {
      background: rgba(255, 255, 255, 0.1);
    }

    .progress-fill {
      background: linear-gradient(90deg, #ffc53d 0%, #ffec3d 100%);
    }

    .remark-section {
      background: rgba(24, 144, 255, 0.1);
      border-color: rgba(24, 144, 255, 0.2);
    }

    .address-section {
      background: rgba(82, 196, 26, 0.1);
      border-color: rgba(82, 196, 26, 0.2);
    }

    .remark-text,
    .address-text {
      color: rgba(255, 255, 255, 0.7);
    }

    .card-footer {
      border-top-color: rgba(255, 255, 255, 0.1);
    }
  }

  .empty-state {
    background: linear-gradient(135deg, #1f1f1f 0%, #2a2a2a 100%);
  }
}
</style>
