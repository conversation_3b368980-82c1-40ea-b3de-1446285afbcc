import { nodeRequest } from '../index'

// 获取小区档案code
export const getSQL = (sql) => nodeRequest.get(`/api/api/Data/Get_SQL?SQL_string=${sql}`)
// 档案列表
export const getDwd_gwyy_xqxttz = (data) => nodeRequest.post('/api2/1809160547388755968/querycommon/dwd_gwyy_xqxttz/1821096626152935424', data, true)

// 小区档案模糊搜索
export const getFuzzySearch = (name) => nodeRequest.get(`/api2/1809160547388755968/querycommon/dwd_gwyy_xqxttz/1833038624355127296?Xqmc=${name}`)

// ---------------------------------------- 新接口 ---------------------------------------------
// 获取小区列表
export const zoneRecordList = (PageNumber, sws) => {
  if (sws) {
    return nodeRequest.get(`/api2/1809160547388755968/querycommon/dwd_gwyy_xqxttz/1916312690746331136?sws=${sws}&PageNumber=${PageNumber}&Pagesize=16`)
  }
  return nodeRequest.get(`/api2/1809160547388755968/querycommon/dwd_gwyy_xqxttz/1916312690746331136?PageNumber=${PageNumber}&Pagesize=16`)
}

export const getFuzzyDetail = (xqbm) => nodeRequest.get(`/api2/1809160547388755968/querycommon/dwd_gwyy_xqxttz/1821096626152935424?xqbm=${xqbm}`)

export const getFuzzyZoneDetail = (xqbm) => nodeRequest.get(`/nodeServer/uptown_material/zone?xqbm=${xqbm}`) //档案详情
