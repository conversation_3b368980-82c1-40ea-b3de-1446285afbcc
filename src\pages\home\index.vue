<template>
  <div class="all flex f-column">
    <div :style="{ paddingTop: `${statusBarHeight}px` }" class="absolute box-shadow back-white W100 z-index-999"><MapSearch @focus="show = -1" @select="mapSearch" /></div>
    <!-- 地图 -->
    <div class="f-1"><MapGlS :data="dots" ref="mapComponentRef" @mapClick="mapClick" /></div>
    <!-- 导航栏 -->
    <Menu @change="handlerChange" v-show="show === -1" />
    <PopupMenu v-model="show" @horizontalSlip="handlerHorizontalSlip">
      <!-- 小区名称 -->
      <template #menu>
        <div class="f-xy-center fon-W700 fon-S28">{{ detail.Zone_Name }}</div>
      </template>
      <!-- 小区基本属性 -->
      <template #interlayer><ZoneDetail :list :detail v-model="current" :show /></template>
      <!-- 列表详情 -->
      <template #content><ZoneContent :current :detail /></template>
    </PopupMenu>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import PopupMenu from './PopupMenu/index.vue'
import MapGlS from './compoents/MapGlS/index.vue'
import Menu from './compoents/Menu/index.vue'
import MapSearch from './compoents/MapSearch/index.vue'
import ZoneDetail from './compoents/ZoneDetail/index.vue'
import ZoneContent from './compoents/ZoneContent/index.vue'
import uniUtil from '/src/utils/uniUtil'
import { onBackPress } from '@dcloudio/uni-app'

import { useCommonStore } from '/src/store/common.js'
import { storeToRefs } from 'pinia'
import { getZoneData } from '/src/services/model/map.js'
import { userHomeStore } from '/src/store/home.js'

const show = ref(-1)
const mapComponentRef = ref(null)
const current = ref('简介')
const list = ref(['简介', '档案', '二供', '总阀总表', '日常维护'])

const { screenInformation } = storeToRefs(useCommonStore())
const { dots } = storeToRefs(userHomeStore())
const { statusBarHeight } = screenInformation.value

watch(show, (val) => (val === -1 ? uni.setNavigationBarTitle({ title: '数据支持系统' }) : ''))

onBackPress(() => {
  if (show.value !== -1) {
    show.value = -1
    return true
  }
})

// 菜单跳转页面
function handlerChange(val) {
  uni.vibrateShort()
  uniUtil.navigateTo(val.path)
}
const detail = ref({}) //区块详情

// 地图区块的点击事件
function mapClick(val) {
  detail.value = val
  show.value = 0
}

// 左右滑动
function handlerHorizontalSlip(val) {
  if (show.value !== 1) return
  const index = list.value.indexOf(current.value)
  if (!val) {
    if (index === 0) return
    current.value = list.value[index - 1]
  } else {
    if (index === list.value.length - 1) return
    current.value = list.value[index + 1]
  }
}

async function mapSearch(item) {
  try {
    item.Center_Point[1] = Number(item.Center_Point[1]) - 0.0025
    const result = await getZoneData(item.Zone_Code)
    const [res] = JSON.parse(result.data)
    mapClick(res)
    mapComponentRef.value.reach(item) //跳转与高亮
  } catch (error) {
    uniUtil.showToast(error)
  }
}
</script>
