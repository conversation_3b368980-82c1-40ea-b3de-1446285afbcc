import { nodeRequest } from '../index'

export const getSQL = (sql) => nodeRequest.get(`/api/api/Data/Get_SQL?SQL_string=${sql}`)
export const zoneRecordList = (params) => nodeRequest.get(`/nodeServer/uptown_material/list`, { params }) // 档案列表
export const getFuzzyDetail = (xqbm) => nodeRequest.get(`/nodeServer/uptown_material?code=${xqbm}`) //档案详情
export const getFuzzyZoneDetail = (xqbm) => nodeRequest.get(`/nodeServer/uptown_material/zone?xqbm=${xqbm}`) //档案详情
// 水池箱档案
export const createPoolTank = (data) => nodeRequest.post(`/nodeServer/uptown_material/pool_tank`, data) //创建档案详情
export const updatePoolTank = (data) => nodeRequest.put(`/nodeServer/uptown_material/pool_tank`, data) //修改档案详情
