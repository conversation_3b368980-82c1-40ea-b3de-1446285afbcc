<template>
  <div class="all back-white flex f-column">
    <yt-upload childId="upload1" ref="ytUploadRef" height="80rpx" width="80rpx" :option :count="1" :size="120" :debug="false" :instantly="false" @progress="progress" @change="changeFile" @uploadEnd="uploadEndFn">
      <view class="upload-btn-simple">
        <wd-icon name="upload" color="#000" size="82rpx"></wd-icon>
      </view>
    </yt-upload>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref, reactive } from 'vue'
import { onShow, onLoad } from '@dcloudio/uni-app'
import { CommonApi } from '/src/services/model/common.js'
import uniUtil from '/src/utils/uniUtil.js'
import { useToast } from 'wot-design-uni'

const toast = useToast()

const ytUploadRef = ref(null)

const option = ref({ url: null, name: 'file', formData: {} })

async function changeFile(e) {
  if (e.size === 0 || e.type == 'change') return
  try {
    toast.loading('文件上传中...')
    const key = e.keys().next().value // 获取文件名
    //获取文件后缀
    const suffix = '.' + key.substring(key.lastIndexOf('.') + 1)

    console.log('上传文件执行', key, suffix)

    const { data } = await CommonApi.ossVoucher({ privately: 1 }) // 获取上传凭证
    option.value.url = data.host
    option.value.formData = { ...data, key: 'pump-house/21/' + key } // 设置上传参数
    ytUploadRef.value.setData(option.value) // 设置上传参数
    ytUploadRef.value.upload(key) // 上传
    toast.close()
    toast.success('文件上传成功')
  } catch (error) {
    toast.close()
    toast.error('文件上传失败')
  }
}

function uploadEndFn(e) {
  // console.log('上传完成', e)
  ytUploadRef.value.clear() // 清空上传列表
}
</script>

<style lang="less" scoped></style>
