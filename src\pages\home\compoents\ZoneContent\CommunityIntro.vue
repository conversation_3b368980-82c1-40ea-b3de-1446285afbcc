<template>
  <div class="info-card community-intro" v-if="detail">
    <div class="card-header">
      <div class="header-icon">
        <wd-icon name="document" size="18px" color="#667eea"></wd-icon>
      </div>
      <div class="header-title">小区简介</div>
    </div>

    <div class="intro-content">
      <!-- 小区图片展示 -->
      <div class="image-container" v-if="hasImageData">
        <div class="image-wrapper" @click="previewImage">
          <!-- 加载状态 -->
          <div class="image-loading" v-if="imageLoading && !isPreviewing">
            <wd-loading type="circular" size="24px" color="#667eea" />
            <span class="loading-text">加载中...</span>
          </div>

          <!-- 图片 -->
          <image
            class="intro-image"
            :class="{ 'image-loaded': !imageLoading, 'image-error': imageError }"
            :src="imageUrl"
            mode="aspectFill"
            @load="onImageLoad"
            @error="onImageError"
            :style="{ opacity: imageLoading && !isPreviewing ? 0 : 1 }"
          />

          <!-- 错误状态 -->
          <div class="image-error-state" v-if="imageError && !isPreviewing">
            <wd-icon name="picture" size="32px" color="#ccc" />
            <span class="error-text">图片加载失败</span>
          </div>

          <!-- 预览提示 -->
          <div class="image-overlay" v-if="!imageLoading && !imageError && !isPreviewing">
            <div class="preview-hint">
              <wd-icon name="eye" size="20px" color="#fff" />
              <span class="hint-text">点击预览</span>
            </div>
          </div>

          <!-- 图片标签 -->
          <div class="image-tag" v-if="!imageLoading && !imageError && !isPreviewing">
            <wd-icon name="camera" size="14px" color="#fff" />
            <span class="tag-text">小区实景</span>
          </div>
        </div>
      </div>

      <div class="intro-text">
        <p class="intro-paragraph">
          <span class="highlight-text">{{ detail.xqmc || '该小区' }}</span
          >位于 <span class="location-text">{{ detail.districtname || '--' }}{{ detail.ssjd || '--' }}{{ detail.communityname || '--' }}</span
          >， 属于<span class="category-text">{{ detail.xqlb || '住宅' }}</span
          >类型小区， 由<span class="management-text">{{ detail.deptname || '--' }}</span
          >负责管理。
        </p>

        <p class="intro-paragraph">
          小区共有<span class="metric-text">{{ getBlockCount(detail.block_number) }}栋</span>建筑， 服务<span class="metric-text">{{ detail.usercount || '--' }}户</span>居民，
          {{ detail.is_pump_room && detail.is_pump_room.includes('否') ? '未设' : '设有' }}二次供水泵房设施。
          {{ detail.jsgl?.length ? `小区共有${detail.jsgl.length}路进水管线，` : '' }}
          供水方式为<span class="supply-text">{{ detail.gsfs || '--' }}</span
          >。
        </p>

        <!-- 泵房改造情况 -->
        <div class="renovation-summary" v-if="detail.pump_room_status && detail.pump_room_status !== '--'">
          <div class="renovation-header">
            <wd-icon name="tool" size="16px" color="#722ed1"></wd-icon>
            <span class="renovation-title">泵房改造情况</span>
          </div>
          <p class="renovation-text">
            当前泵房状态为<span class="status-highlight" :class="getStatusClass(detail.pump_room_status)">{{ getStatusText(detail.pump_room_status) }}</span
            >。
            <template v-if="detail.pump_room_status === '已通水'">
              改造工程已完成，泵房数量从
              <span class="before-after">{{ detail.before_pump_room_count || 0 }}座</span>
              {{ detail.bfsl && detail.before_pump_room_count !== null && detail.before_pump_room_count !== undefined && detail.bfsl !== detail.before_pump_room_count ? `调整为${detail.bfsl}座` : '保持不变' }}， 加压户数从
              <span class="before-after">{{ detail.before_number_pressurized || 0 }}户</span>
              {{ detail.jyhs && detail.before_number_pressurized !== null && detail.before_number_pressurized !== undefined && detail.jyhs !== detail.before_number_pressurized ? `增至${detail.jyhs}户` : '保持不变' }}。
              {{ detail.eggzwcnd ? `改造于${detail.eggzwcnd?.slice(0, 10)}年完成。` : '' }}
            </template>
            <template v-else-if="detail.pump_room_status === '已立项施工中(指临供状态)'">
              改造工程正在进行中，目前处于临时供水阶段。
              {{ detail.egcsjsnd ? `临时供水自${detail.egcsjsnd}开始。` : '' }}
            </template>
            <template v-else-if="detail.pump_room_status === '已立项未进场'"> 改造工程已立项，计划改造泵房{{ detail.before_pump_room_count || '--' }}座， 服务{{ detail.before_number_pressurized || '--' }}户居民。 </template>
            <template v-else-if="detail.pump_room_status === '无需二供改造'"> 该小区供水设施运行良好，无需进行二次供水改造。 </template>
          </p>
        </div>

        <!-- 管网改造情况 -->
        <div class="pipeline-summary" v-if="detail.sftbgz">
          <div class="pipeline-header">
            <wd-icon name="link" size="16px" color="#52c41a"></wd-icon>
            <span class="pipeline-title">管网改造情况</span>
          </div>
          <p class="pipeline-text">
            <template v-if="handlerBoolean(detail.sftbgz) === '是'">
              小区已完成提标改造，
              {{ detail.gzwcnd ? `于${detail.gzwcnd.slice(0, 4)}年完成改造。` : '' }}
              埋地管材由<span class="material-change">{{ handlerlist(detail.mdgc) || '--' }}</span> 更换为<span class="material-change">{{ handlerlist(detail.buried_pipes_after) || '--' }}</span
              >， 爬墙管材由<span class="material-change">{{ handlerlist(detail.pqggc) || '--' }}</span> 更换为<span class="material-change">{{ handlerlist(detail.wall_climbing_pipe_after) || '--' }}</span
              >。
              {{ detail.excellent_drink_batch ? `属于优饮${detail.excellent_drink_batch}批次项目。` : '' }}
            </template>
            <template v-else>
              小区尚未进行提标改造， 当前埋地管材为<span class="material-info">{{ handlerlist(detail.mdgc) || '--' }}</span
              >， 爬墙管材为<span class="material-info">{{ handlerlist(detail.pqggc) || '--' }}</span
              >。
            </template>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch, ref } from 'vue'
import { getFuzzyZoneDetail } from '/src/services/model/material.js'

const props = defineProps({ detail: { type: Object, required: true } })

// 图片相关状态
const imageUrl = ref('')
const imageLoading = ref(false)
const imageError = ref(false)
const hasImageData = ref(false)
const isPreviewing = ref(false) // 添加预览状态管理
const loadedImageCache = ref(new Map()) // 添加图片缓存

// 监听detail变化，获取图片
watch(
  () => props.detail,
  async (val) => {
    if (!val?.xqbm) {
      hasImageData.value = false
      return
    }

    const cacheKey = val.xqbm

    // 检查缓存中是否已有该图片信息
    if (loadedImageCache.value.has(cacheKey)) {
      const cachedData = loadedImageCache.value.get(cacheKey)
      imageUrl.value = cachedData.url
      imageError.value = cachedData.error
      imageLoading.value = false
      hasImageData.value = !cachedData.error
      return
    }

    try {
      imageLoading.value = true
      imageError.value = false
      hasImageData.value = true
      const { data } = await getFuzzyZoneDetail(val.xqbm)
      if (data.length) {
        const url = `https://www.szwgft.cn/nodeServer/img/FTImg/${data[0].Zone_Code}.png`
        imageUrl.value = url
        // 缓存成功的图片信息
        loadedImageCache.value.set(cacheKey, { url, error: false })
        // 注意：这里不设置 imageLoading.value = false，因为图片还需要通过 onImageLoad 来确认真正加载完成
      } else {
        // 如果没有数据，显示无图片状态
        imageLoading.value = false
        imageError.value = true
        imageUrl.value = ''
        // 缓存错误状态
        loadedImageCache.value.set(cacheKey, { url: '', error: true })
      }
    } catch (error) {
      console.error('获取小区图片失败:', error)
      imageError.value = true
      imageLoading.value = false
      // 缓存错误状态
      loadedImageCache.value.set(cacheKey, { url: '', error: true })
    }
  },
  { immediate: true }
)

// 图片加载成功
function onImageLoad() {
  // 只有在非预览状态下才更新加载状态
  if (!isPreviewing.value) {
    imageLoading.value = false
    imageError.value = false
  }
}

// 图片加载失败
function onImageError() {
  // 只有在非预览状态下才更新错误状态
  if (!isPreviewing.value) {
    imageLoading.value = false
    imageError.value = true
  }
}

// 预览图片
function previewImage() {
  if (imageUrl.value && !imageError.value && !isPreviewing.value) {
    isPreviewing.value = true
    uni.previewImage({
      urls: [imageUrl.value],
      current: imageUrl.value,
      success: () => {
        console.log('图片预览成功')
      },
      fail: (error) => {
        console.error('图片预览失败:', error)
      },
      complete: () => {
        // 预览完成后重置状态，确保图片状态正确
        isPreviewing.value = false
        // 如果图片已经加载过，确保不会重新显示加载状态
        if (imageUrl.value && !imageError.value) {
          imageLoading.value = false
        }
      }
    })
  }
}

// 获取楼栋数量
function getBlockCount(val) {
  if (!val || val == '""') return '0'
  const list = val.split(';')
  const total = list.reduce((total, item) => {
    let match = item.match(/栋数：(\d+)栋/)
    if (match) {
      let count = parseInt(match[1], 10)
      return total + count
    } else {
      return total
    }
  }, 0)
  return total.toString()
}

// 获取状态样式类
function getStatusClass(status) {
  const statusMap = {
    无需二供改造: 'status-complete',
    查漏补缺: 'status-partial',
    已立项未进场: 'status-pending',
    '已立项施工中(指临供状态)': 'status-progress',
    已通水: 'status-complete'
  }
  return statusMap[status] || 'status-unknown'
}

// 获取状态文本
function getStatusText(status) {
  const textMap = {
    无需二供改造: '无需改造',
    查漏补缺: '查漏补缺',
    已立项未进场: '待进场',
    '已立项施工中(指临供状态)': '施工中',
    已通水: '已完成'
  }
  return textMap[status] || '未知状态'
}

// 处理布尔值
function handlerBoolean(value) {
  return value === true ? '是' : value === false ? '否' : value
}

// 处理列表数据
function handlerlist(value) {
  if (!value || value == '""') return ''
  if (/^[\[]/.test(value)) {
    try {
      // 尝试解析为 JSON 数组
      return JSON.parse(value).join('、')
    } catch (error) {
      // 如果解析失败，手动处理类似 [不锈钢] 的格式
      const match = value.match(/^\[(.+)\]$/)
      if (match) {
        // 移除方括号，按逗号分割（如果有的话），然后用顿号连接
        return match[1]
          .split(',')
          .map((item) => item.trim())
          .join('、')
      }
      // 如果都不匹配，返回原值去掉引号
      return value.replace(/"/g, '')
    }
  }
  return value.replace(/"/g, '')
}
</script>

<style lang="less" scoped>
.info-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border-left: 4rpx solid #e8e8e8;

  &.community-intro {
    border-left-color: #667eea;
  }
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.header-icon {
  width: 36rpx;
  height: 36rpx;
  border-radius: 10rpx;
  background: rgba(77, 99, 224, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12rpx;
}

.header-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.2;
}

// 小区简介卡片样式
.intro-content {
  padding: 0;
}

// 图片展示样式
.image-container {
  margin-bottom: 20rpx;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 200rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
  }
}

.intro-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;

  &.image-loaded {
    opacity: 1;
  }

  &.image-error {
    opacity: 0;
  }
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 2;
}

.loading-text {
  font-size: 22rpx;
  color: #667eea;
  margin-top: 8rpx;
  font-weight: 500;
}

.image-error-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(248, 249, 250, 0.95);
  z-index: 2;
}

.error-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 8rpx;
  font-weight: 500;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 1;

  .image-wrapper:hover & {
    opacity: 1;
  }
}

.preview-hint {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.hint-text {
  font-size: 20rpx;
  margin-top: 4rpx;
  font-weight: 500;
}

.image-tag {
  position: absolute;
  top: 12rpx;
  left: 12rpx;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  color: #fff;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  font-size: 20rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
  z-index: 2;
}

.tag-text {
  margin-left: 4rpx;
}

.intro-text {
  line-height: 1.6;
}

.intro-paragraph {
  margin: 0 0 16rpx 0;
  font-size: 26rpx;
  color: #333;
  line-height: 1.7;
  text-align: justify;
}

.highlight-text {
  font-weight: 700;
  color: #667eea;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin: 0 4rpx;
}

.location-text {
  font-weight: 600;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}

.category-text {
  font-weight: 600;
  color: #722ed1;
  background: rgba(114, 46, 209, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}

.management-text {
  font-weight: 600;
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}

.metric-text {
  font-weight: 700;
  color: #fa8c16;
  background: rgba(250, 140, 22, 0.1);
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin: 0 2rpx;
}

.supply-text {
  font-weight: 600;
  color: #13c2c2;
  background: rgba(19, 194, 194, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}

// 泵房改造情况样式
.renovation-summary {
  margin-top: 20rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.03) 0%, rgba(114, 46, 209, 0.01) 100%);
  border-radius: 12rpx;
  border-left: 3rpx solid #722ed1;
}

.renovation-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.renovation-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #722ed1;
  margin-left: 8rpx;
}

.renovation-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.status-highlight {
  font-weight: 700;
  padding: 2rpx 8rpx;
  border-radius: 8rpx;
  margin: 0 2rpx;

  &.status-complete {
    color: #52c41a;
    background: rgba(82, 196, 26, 0.15);
  }

  &.status-progress {
    color: #722ed1;
    background: rgba(114, 46, 209, 0.15);
  }

  &.status-pending {
    color: #1890ff;
    background: rgba(24, 144, 255, 0.15);
  }

  &.status-partial {
    color: #fa8c16;
    background: rgba(250, 140, 22, 0.15);
  }
}

.before-after {
  font-weight: 600;
  color: #eb2f96;
  background: rgba(235, 47, 150, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}

// 管网改造情况样式
.pipeline-summary {
  margin-top: 16rpx;
  padding: 16rpx;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.03) 0%, rgba(82, 196, 26, 0.01) 100%);
  border-radius: 12rpx;
  border-left: 3rpx solid #52c41a;
}

.pipeline-header {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.pipeline-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #52c41a;
  margin-left: 8rpx;
}

.pipeline-text {
  font-size: 24rpx;
  color: #555;
  line-height: 1.6;
  margin: 0;
}

.material-change {
  font-weight: 600;
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}

.material-info {
  font-weight: 600;
  color: #666;
  background: rgba(102, 102, 102, 0.1);
  padding: 2rpx 6rpx;
  border-radius: 6rpx;
  margin: 0 2rpx;
}
</style>
