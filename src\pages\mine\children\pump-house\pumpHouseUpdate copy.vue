<template>
  <view class="pump-house-update" v-if="detail">
    <!-- 页面头部装饰 -->
    <view class="page-header">
      <view class="header-decoration">
        <view class="decoration-circle circle-1"></view>
        <view class="decoration-circle circle-2"></view>
        <view class="decoration-circle circle-3"></view>
      </view>
    </view>

    <!-- 基础信息表单 -->
    <view class="form-section basic-section">
      <view class="section-header">
        <view class="section-icon basic-icon">
          <wd-icon name="setting" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">基础信息</text>
          <text class="title-subtitle">泵房基本信息和状态设置</text>
        </view>
      </view>

      <view class="form-content">
        <wd-cell-group custom-class="form-group" border>
          <wd-input label="泵房名称" label-width="220rpx" show-word-limit prop="PumpHouseName" suffix-icon="warn-bold" clearable v-model="detail.PumpHouseName" placeholder="请输入泵房名称" />
          <wd-picker label="改造状态" placeholder="选择状态" label-width="220rpx" prop="RemouldState" v-model="detail.RemouldState" :columns="selectOption.GZ" />
          <wd-picker label="泵房批次" placeholder="选择批次" label-width="220rpx" prop="Batch" v-model="detail.Batch" :columns="selectOption.LX" />
          <wd-input label="小区地址" label-width="220rpx" show-word-limit prop="ResidentialAddress" suffix-icon="warn-bold" clearable v-model="detail.ResidentialAddress" placeholder="请输入地址" />
          <wd-input label="加压供水户数" type="number" label-width="220rpx" show-word-limit prop="PressurizedHouseholds" suffix-icon="warn-bold" clearable v-model="detail.PressurizedHouseholds" placeholder="请输入加压户数" />
          <wd-input label="小区建设时间" label-width="220rpx" show-word-limit prop="ConstructionTime" suffix-icon="warn-bold" clearable v-model="detail.ConstructionTime" placeholder="请输入建设时间" />
          <wd-input label="泵房管理状态" label-width="220rpx" show-word-limit prop="PumpRoomControlledState" suffix-icon="warn-bold" clearable v-model="detail.PumpRoomControlledState" placeholder="请输入管理状态" />
        </wd-cell-group>
      </view>
    </view>

    <!-- 物业信息表单 -->
    <view class="form-section property-section">
      <view class="section-header">
        <view class="section-icon property-icon">
          <wd-icon name="home" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">物业信息</text>
          <text class="title-subtitle">物业管理单位和联系方式</text>
        </view>
      </view>

      <view class="form-content">
        <wd-cell-group custom-class="form-group" border>
          <wd-input label="物业单位" label-width="220rpx" show-word-limit prop="PropertyUnit" suffix-icon="warn-bold" clearable v-model="detail.PropertyUnit" placeholder="请输入物业管理单位" />
          <wd-input label="物业联系人" label-width="220rpx" show-word-limit prop="ContactPerson" suffix-icon="warn-bold" clearable v-model="detail.ContactPerson" placeholder="请输入联系人" />
          <wd-input label="物业电话" label-width="220rpx" show-word-limit prop="PhoneNumber" suffix-icon="warn-bold" clearable v-model="detail.PhoneNumber" placeholder="请输入电话" />
        </wd-cell-group>
      </view>
    </view>

    <!-- 项目信息表单 -->
    <view class="form-section project-section">
      <view class="section-header">
        <view class="section-icon project-icon">
          <wd-icon name="folder" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">项目信息</text>
          <text class="title-subtitle">项目进展和施工管理信息</text>
        </view>
      </view>

      <view class="form-content">
        <wd-cell-group custom-class="form-group" border>
          <wd-picker label="项目进展状态" placeholder="选择进展状态" label-width="250rpx" prop="ProgressStatus" v-model="detail.ProgressStatus" :columns="selectOption.ZT" />
          <wd-picker label="运营管理状态" placeholder="选择管理状态" label-width="220rpx" prop="Batch" v-model="detail.OperationManagementState" :columns="selectOption.YY" />
          <wd-input label="施工单位" label-width="250rpx" show-word-limit prop="ConstructionUnit" suffix-icon="warn-bold" clearable v-model="detail.ConstructionUnit" placeholder="请输入施工单位" />
          <wd-input label="现场监管责任人" label-width="250rpx" show-word-limit prop="PersonInCharge" suffix-icon="warn-bold" clearable v-model="detail.PersonInCharge" placeholder="请输入责任人" />
          <wd-input label="临供停水事件数" label-width="250rpx" show-word-limit prop="TemporarySupplyEvents" suffix-icon="warn-bold" clearable v-model="detail.TemporarySupplyEvents" placeholder="请输入停水数" />
          <wd-calendar label="初步验收时间" label-width="250rpx" v-model="detail.AcceptanceTime" />
          <wd-textarea label="备注" auto-height type="textarea" v-model="detail.Remark" :maxlength="200" show-word-limit placeholder="请输入备注信息" clearable prop="Remark" />
        </wd-cell-group>
      </view>
    </view>

    <!-- 位置信息表单 -->
    <view class="form-section location-section">
      <view class="section-header">
        <view class="section-icon location-icon">
          <wd-icon name="location" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">位置信息</text>
          <text class="title-subtitle">地理位置和图片资料</text>
        </view>
      </view>

      <view class="form-content">
        <wd-cell-group custom-class="form-group" border>
          <wd-cell title="坐标" :value="detail.X + ',' + detail.Y" />
          <wd-cell title="所属街道" :value="detail.BelongingStreet" />
          <wd-cell title="所属片区" prop="belongingArea" :value="detail.BelongingArea" />
          <wd-cell title="所属网格" :value="detail.Gridding" />
          <wd-textarea label="小区地址" auto-height type="textarea" v-model="detail.ResidentialAddress" :maxlength="200" show-word-limit placeholder="请输入地址信息" clearable prop="ResidentialAddress" />
          <wd-textarea label="泵房精确位置" auto-height type="textarea" v-model="detail.AccuratePosition" :maxlength="200" show-word-limit placeholder="泵房精确位置填写规则 如：xxx小区xxx栋地下二层" clearable prop="AccuratePosition" />
        </wd-cell-group>

        <!-- 位置更新按钮 -->
        <view class="location-update-btn" @click="open = true">
          <wd-icon name="location" size="32rpx" color="#ffffff" />
          <text class="update-btn-text">更新位置</text>
        </view>

        <!-- 图片上传区域 -->
        <view class="image-upload-section">
          <view class="upload-title">泵房图片</view>
          <view class="upload-container">
            <UploadeImg :maxlength="3" :handleImg="({ data }) => data" :url v-model="detail.PumpHouseImg" />
          </view>
        </view>
      </view>
    </view>

    <!-- 节点信息表单 -->
    <view class="form-section node-section" v-if="detail">
      <view class="section-header">
        <view class="section-icon node-icon">
          <wd-icon name="list" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">节点信息</text>
          <text class="title-subtitle">项目节点进度和文件管理</text>
        </view>
      </view>

      <view class="form-content">
        <view class="node-tabs-container">
          <wd-tabs v-model="CurrentNode" custom-class="">
            <block v-for="(item, index) in pumpHouseNodeKeys" :key="item">
              <wd-tab :title="`${item}`">
                <view class="node-content">
                  <view v-if="pumpHouseNodeDetail" class="node-detail">
                    <!-- 完成状态 -->
                    <view class="status-section">
                      <view class="status-label">完成状态</view>
                      <wd-radio-group v-model="pumpHouseNodeDetail.IsEnd" shape="button" custom-class="status-radio">
                        <wd-radio :value="true">是</wd-radio>
                        <wd-radio :value="false">否</wd-radio>
                      </wd-radio-group>
                    </view>

                    <!-- 完成时间 -->
                    <view class="time-section">
                      <wd-calendar label="完成时间" v-model="pumpHouseNodeDetail.CompletionTime" />
                    </view>

                    <!-- 备注信息 -->
                    <view class="remark-section">
                      <wd-textarea label="备注信息" auto-height type="textarea" v-model="pumpHouseNodeDetail.Remark" :maxlength="200" show-word-limit placeholder="请输入备注信息" clearable prop="content" />
                    </view>

                    <!-- 文件上传区域 -->
                    <view class="file-section" v-if="pumpHouseNodeDetail?.Files">
                      <view class="file-header">
                        <wd-icon name="folder" size="32rpx" color="#722ed1" />
                        <text class="file-title">需要上传的文件</text>
                      </view>
                      <view class="file-list">
                        <template v-for="fileItem in pumpHouseNodeFileKeyMap.get(index + 1)" :key="fileItem.Id">
                          <view class="file-item">
                            <view class="file-info">
                              <text class="file-name" :class="{ 'file-completed': pumpHouseNodeDetail.Files.includes(fileItem.DictCode) }">
                                {{ fileItem.DictValue }}
                              </text>
                              <view class="file-status" v-if="pumpHouseNodeDetail.Files.includes(fileItem.DictCode)">
                                <wd-icon name="check" size="24rpx" color="#52c41a" />
                                <text class="status-text">已上传</text>
                              </view>
                            </view>
                            <view class="file-action" v-if="!pumpHouseNodeDetail.Files.includes(fileItem.DictCode)">
                              <yt-upload
                                childId="upload1"
                                height="80rpx"
                                width="80rpx"
                                :option="{
                                  url: `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${detail.PumpRoomNumber}/${index + 1}`,
                                  name: 'file',
                                  header
                                }"
                                :size="120"
                                :debug="false"
                                :instantly="true"
                                @progress="progress"
                                @uploadEnd="(e) => uploadEnd(e, index + 1, fileItem.DictCode)"
                              >
                                <view class="upload-btn-simple">
                                  <wd-icon name="upload" color="#fff" size="32rpx"></wd-icon>
                                </view>
                              </yt-upload>
                            </view>
                          </view>
                        </template>
                      </view>
                    </view>
                  </view>

                  <!-- 空状态 -->
                  <view v-else class="empty-state">
                    <view class="empty-content">
                      <wd-icon name="folder-open" size="96rpx" color="#d9d9d9" />
                      <text class="empty-text">暂无节点内容</text>
                      <view class="add-btn" @click="addNode(index + 1)">
                        <wd-icon name="add" size="24rpx" color="#722ed1" />
                        <text class="add-text">添加节点信息</text>
                      </view>
                    </view>
                  </view>
                </view>
              </wd-tab>
            </block>
          </wd-tabs>
        </view>
      </view>
    </view>
    <!-- 优化后的提交按钮 -->
    <view class="submit-section">
      <view class="submit-button-container">
        <wd-button custom-class="submit-button" :loading="isSubmitting" :disabled="isSubmitting" @click="handelUpdateClick">
          <view class="button-content">
            <wd-icon v-if="!isSubmitting" name="check-bold" size="32rpx" color="#ffffff" />
            <text class="button-text">{{ isSubmitting ? '保存中...' : '保存修改' }}</text>
          </view>
        </wd-button>
      </view>
    </view>

    <CoordinatePickup v-if="detail && open" v-model="open" :MarkerCenter @change="change" />

    <!-- 优化后的上传进度模态框 -->
    <view class="upload-modal" v-if="show">
      <view class="upload-modal-overlay" @click.stop></view>
      <view class="upload-modal-content">
        <view class="upload-header">
          <view class="upload-icon">
            <wd-icon name="upload" size="48rpx" color="#1890ff" />
          </view>
          <view class="upload-title">文件上传中</view>
          <view class="upload-subtitle">请稍候，正在处理您的文件...</view>
        </view>

        <view class="upload-progress">
          <view class="progress-container">
            <wd-progress :percentage="percentage" :show-text="false" stroke-width="8rpx" color="#1890ff" />
            <view class="progress-text">{{ percentage }}%</view>
          </view>
          <wd-loading size="32rpx" />
        </view>
      </view>
    </view>
  </view>
  <wd-toast />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import { PumpHouseApi } from '/src/services/model/pump.house.js'
import { CommonApi } from '/src/services/model/common.js'
import CoordinatePickup from './components/CoordinatePickup/index.vue'
import uniUtil from '/src/utils/uniUtil.js'
import dayjs from 'dayjs'
import UploadeImg from '/src/components/UploadeImg/index.vue'

import debounce from 'lodash/debounce'
import { useToast } from 'wot-design-uni'

const toast = useToast()
const detail = ref(null)
const pumpHouseNode = ref([])
const open = ref(false)
const show = ref(false)
const MarkerCenter = ref(null)
const CurrentNode = ref(0)
const percentage = ref(0)
const isSubmitting = ref(false)

const selectOption = ref({})
const pumpHouseNodeKeys = computed(() => pumpHouseNode.value.map((item) => item.DictValue))
const header = computed(() => ({ authorization: uniUtil.get('userInfo')?.token }))
const url = computed(() => `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${detail.value.PumpRoomNumber}/img`)

onLoad(async ({ PumpRoomNumber }) => {
  const { data } = await PumpHouseApi.detail(PumpRoomNumber)
  detail.value = data
  pumpHouseNode.value = await queryDictionaries('pumpHouseNode')
  CurrentNode.value = detail.value.CurrentNode - 1
  MarkerCenter.value = [data.X, data.Y]

  // 等待优化
  const pumpHouseProjectStatus = await queryDictionaries('pumpHouseProjectStatus')
  selectOption.value.ZT = pumpHouseProjectStatus.map((item) => ({ label: item.DictValue, value: item.DictValue })) //项目进展状态
  const pumpHouseProjectBatch = await queryDictionaries('pumpHouseProjectBatch')
  selectOption.value.LX = pumpHouseProjectBatch.map((item) => ({ label: item.DictValue, value: item.DictCode })) //泵房批次
  const pumpHouseOperationState = await queryDictionaries('pumpHouseOperationState')
  selectOption.value.YY = pumpHouseOperationState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //运营管理状态
  const pumpHouseRemouldState = await queryDictionaries('pumpHouseRemouldState')
  selectOption.value.GZ = pumpHouseRemouldState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //改造状态
})

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.queryDictionaries(type)
    return data
  } catch (error) {
    message.error(error.message)
  }
}

function change(e) {
  detail.value.X = e.x.toString()
  detail.value.Y = e.y.toString()
  detail.value.Gridding = e.feature.Grid.replace('FD', '福东').replace('FZ', '福中').replace('ML', '梅林').replace('XM', '香蜜')
  detail.value.BelongingStreet = e.feature.Subdistric
  detail.value.ZoneCode = e.feature.Zone_Code
  detail.value.BelongingArea = e.feature.Belong_Are
  open.value = false
}

const pumpHouseNodeDetail = ref(null)
const pumpHouseNodeFileKeyMap = new Map()
watch(CurrentNode, async (val) => {
  if (!pumpHouseNodeFileKeyMap.has(val + 1)) {
    const { data: FileKey } = await CommonApi.queryDictionaries(`pumpHouseNodeFile_${val + 1}`)
    pumpHouseNodeFileKeyMap.set(val + 1, FileKey)
  }
  const { data } = await PumpHouseApi.nodeDetail(detail.value.PumpRoomNumber, val + 1)
  if (data) {
    data.Remark = data.Remark !== null && data.Remark ? data.Remark : ''
    if (data?.Files) {
      data.Files = JSON.parse(data.Files).map(({ FileType }) => FileType)
    } else {
      data.Files = []
    }
  }

  pumpHouseNodeDetail.value = data
})

const debounceWatchPumpRoomNumberCallback = debounce(WatchPumpRoomNumberCallback, 1000)
watch([pumpHouseNodeDetail, () => pumpHouseNodeDetail.value.IsEnd, () => pumpHouseNodeDetail.value.CompletionTime, () => pumpHouseNodeDetail.value.Remark], debounceWatchPumpRoomNumberCallback, { deep: true })
async function WatchPumpRoomNumberCallback([val, IsEnd, CompletionTime, Remark], [oldVal, oldIsEnd, oldCompletionTime, oldRemark]) {
  try {
    if (!val) return
    if (val && val.Node != oldVal.Node) return
    if (IsEnd != oldIsEnd || CompletionTime != oldCompletionTime || Remark != oldRemark) {
      await PumpHouseApi.updateNode(val)
      toast.success('节点信息更新成功')
    }
  } catch (error) {
    console.log(error)
  }
}

async function uploadEnd(e, nodeNum, FileType) {
  try {
    const url = JSON.parse(e.responseText).data
    await PumpHouseApi.createNodeFile({ Path: url, Node: nodeNum, PumpRoomNumber: detail.value.PumpRoomNumber, FileType })
    const { data } = await PumpHouseApi.nodeDetail(detail.value.PumpRoomNumber, nodeNum)
    if (data) {
      data.Remark = data.Remark !== null && data.Remark ? data.Remark : ''
      if (data?.Files) {
        data.Files = JSON.parse(data.Files).map(({ FileType }) => FileType)
      } else {
        data.Files = []
      }
    }
    pumpHouseNodeDetail.value = data
    show.value = false
    await PumpHouseApi.updateNode(data)
    toast.success('文件上传成功，节点信息更新成功')
  } catch (error) {
    toast.error('上传失败')
  }
}

function progress(e) {
  show.value = true
  percentage.value = e.progress
}

async function addNode(nodeNum) {
  try {
    const CompletionTime = dayjs().format('YYYY-MM-DD')
    await PumpHouseApi.createNode({ PumpRoomNumber: detail.value.PumpRoomNumber, Node: nodeNum, CompletionTime })
    const { data } = await PumpHouseApi.nodeDetail(detail.value.PumpRoomNumber, nodeNum)
    if (data) {
      data.Remark = data.Remark !== null && data.Remark ? data.Remark : ''
      if (data?.Files) {
        data.Files = JSON.parse(data.Files).map(({ FileType }) => FileType)
      } else {
        data.Files = []
      }
    }

    pumpHouseNodeDetail.value = data
  } catch (error) {}
}

async function handelUpdateClick() {
  if (isSubmitting.value) return

  try {
    isSubmitting.value = true
    await PumpHouseApi.update(detail.value)
    toast.success('泵房信息更新成功')
    setTimeout(() => {
      isSubmitting.value = false
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    isSubmitting.value = false
    toast.error('泵房信息更新失败')
  }
}
</script>

<style lang="less" scoped>
/* 页面整体样式 */
.pump-house-update {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8faff 0%, #f0f2f5 100%);
  padding: 24rpx;
  padding-bottom: 140rpx; // 为提交按钮留出空间
  position: relative;
}

/* 页面头部装饰 */
.page-header {
  position: relative;
  height: 120rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.header-decoration {
  position: relative;
  width: 100%;
  height: 100%;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(64, 169, 255, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80rpx;
  height: 80rpx;
  top: 20rpx;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  right: 20%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 40rpx;
  right: 10%;
  animation-delay: 4s;
}

/* 表单分组样式 */
.form-section {
  margin-bottom: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: fadeInUp 0.6s ease-out;

  &.basic-section {
    animation-delay: 0.1s;
  }

  &.property-section {
    animation-delay: 0.2s;
  }

  &.project-section {
    animation-delay: 0.3s;
  }

  &.location-section {
    animation-delay: 0.4s;
  }

  &.node-section {
    animation-delay: 0.5s;
  }
}

/* 分组头部样式 */
.section-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.basic-icon {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.property-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.project-icon {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
}

.location-icon {
  background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
}

.node-icon {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}

.section-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.title-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 表单内容样式 */
.form-content {
  padding: 0;
}

:deep(.form-group) {
  border-radius: 0 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 位置信息特殊样式 */
.location-update-btn {
  margin: 24rpx 32rpx;
  padding: 20rpx 32rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 3rpx 12rpx rgba(82, 196, 26, 0.4);
  }
}

.update-btn-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}

.image-upload-section {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.upload-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.upload-container {
  border-radius: 12rpx;
  overflow: hidden;
}

/* 节点信息现代化样式 */
.node-tabs-container {
  padding: 0;
}

:deep(.modern-node-tabs) {
  .wd-tabs__nav {
    background: #f8faff;
    padding: 16rpx 32rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .wd-tab {
    margin-right: 24rpx;
    margin-bottom: 16rpx;
    padding: 12rpx 20rpx;
    border-radius: 12rpx;
    background: #ffffff;
    border: 1rpx solid #e8e8e8;
    transition: all 0.3s ease;
    font-size: 28rpx;

    &.is-active {
      background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
      border-color: #722ed1;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

.node-content {
  padding: 32rpx;
  min-height: 300rpx;
  background: #ffffff;
}

.node-detail {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 状态区域 */
.status-section {
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4ff;
}

.status-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

:deep(.status-radio) {
  .wd-radio {
    margin-right: 16rpx;

    &.is-checked {
      background: #722ed1;
      border-color: #722ed1;
      color: #ffffff;
    }
  }
}

/* 时间和备注区域 */
.time-section,
.remark-section {
  margin-bottom: 8rpx;
}

/* 文件上传区域 */
.file-section {
  margin-top: 8rpx;
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4ff;
}

.file-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e0e6ff;
  margin-bottom: 16rpx;
}

.file-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.file-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;

  &.file-completed {
    color: #52c41a;
  }
}

.file-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #52c41a;
}

.file-action {
  margin-left: 16rpx;
}

.upload-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.4);
  }
}

/* 简化的上传按钮样式，确保app端兼容性 */
.upload-btn-simple {
  width: 80rpx;
  height: 80rpx;
  background-color: #722ed1;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 240rpx;
  padding: 40rpx 20rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.add-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.2);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.3);
  }
}

.add-text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}

/* 提交按钮区域样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, #ffffff 100%);
  padding: 24rpx 24rpx 40rpx;
  z-index: 10;
  backdrop-filter: blur(10rpx);
}

.submit-button-container {
  width: 100%;
  max-width: 600rpx;
  margin: 0 auto;
}

:deep(.submit-button) {
  width: 100% !important;
  height: 96rpx !important;
  border-radius: 24rpx !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border: none !important;
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.3) !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
  }

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4) !important;
  }

  &:not(:disabled):active::before {
    left: 100%;
  }

  &:disabled {
    background: linear-gradient(135deg, #d9d9d9 0%, #f0f0f0 100%) !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
    transform: none !important;
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.button-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
}

.updateIcon {
  top: 16rpx;
  right: 26rpx;
  padding: 4rpx 18rpx;
  background: #15a131;
  border-radius: 12rpx;
}

:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}

/* 优化后的上传模态框样式 */
.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.upload-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
}

.upload-modal-content {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin: 0 40rpx;
  max-width: 500rpx;
  width: 100%;
  box-shadow: 0 24rpx 64rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.upload-icon {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  animation: pulse 2s infinite;
}

.upload-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 12rpx;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.progress-container {
  width: 100%;
  position: relative;
}

.progress-text {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  margin-top: 16rpx;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 动画效果 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .pump-house-update {
    padding: 16rpx;
  }

  .form-section {
    margin-bottom: 24rpx;
    border-radius: 20rpx;
  }

  .section-header {
    padding: 24rpx 24rpx 20rpx;
  }

  .section-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 20rpx;
  }

  .title-text {
    font-size: 30rpx;
  }

  .title-subtitle {
    font-size: 22rpx;
  }

  .location-update-btn {
    margin: 20rpx 24rpx;
    padding: 16rpx 24rpx;
  }

  .image-upload-section {
    padding: 24rpx;
  }

  .node-content {
    padding: 24rpx;
  }

  .status-section {
    padding: 20rpx;
  }

  .file-section {
    padding: 20rpx;
  }

  .file-item {
    padding: 12rpx 16rpx;
  }

  .upload-btn {
    width: 56rpx;
    height: 56rpx;
  }

  .upload-btn-simple {
    width: 72rpx;
    height: 72rpx;
  }

  .upload-modal-content {
    margin: 0 24rpx;
    padding: 40rpx 32rpx;
  }

  .submit-section {
    padding: 20rpx 20rpx 32rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pump-house-update {
    background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
  }

  .form-section {
    background: #333333;
    border-color: #444444;
  }

  .section-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    border-bottom-color: #444444;
  }

  .title-text {
    color: #ffffff;
  }

  .title-subtitle {
    color: #cccccc;
  }

  .image-upload-section {
    background: #2a2a2a;
    border-top-color: #444444;
  }

  .node-content {
    background: #333333;
  }

  .status-section,
  .file-section {
    background: #2a2a2a;
    border-color: #444444;
  }

  .status-label,
  .file-title {
    color: #ffffff;
  }

  .file-header {
    border-bottom-color: #444444;
  }

  .file-item {
    background: #333333;
    border-color: #444444;
  }

  .file-name {
    color: #ffffff;

    &.file-completed {
      color: #52c41a;
    }
  }

  .empty-text {
    color: #cccccc;
  }
}
</style>
