<template>
  <div class="introduce box-shadow border-R12">
    <CommunityIntro v-if="record" :detail="record" />
    <div v-else class="all back-white border-R12 f-xy-center">
      <wd-status-tip image="search" tip="无数据" />
    </div>
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import uniUtil from '/src/utils/uniUtil.js'
import CommunityIntro from './CommunityIntro.vue'

import { getSQL, getFuzzyDetail } from '/src/services/model/material'

const record = ref(null)
const props = defineProps({ detail: Object })

watch(() => props.detail, watchCallback, { immediate: true, deep: true })

async function watchCallback(val) {
  if (!val.Client_Name) return (record.value = null)
  try {
    const [data] = await getFuzzyDetail(val.Client_Name)
    record.value = data
    //   处理进水路数数据
    if (record.value.jsgl == '[]' || !record.value.jsgl || record.value.jsgl?.length < 10) {
      record.value.jsgl = []
    } else {
      record.value.jsgl = JSON.parse(record.value.jsgl)
    }
  } catch (error) {
    uniUtil.showToast(error.message)
  }
}

function handlerAddress(val) {
  return [val?.District, val?.Subdistrict, val?.Community, val?.Road].filter(Boolean).join('')
}

const handlerMaterialsChange = computed(() => `改前（${handlerStr(record.value.mdgc)}）改后（${handlerStr(record.value.buried_pipes_after)}）`)
const handlerMaterialsChange2 = computed(() => `改前（${handlerStr(record.value.pqggc)}）改后（${handlerStr(record.value.wall_climbing_pipe_after)}）`)

const handlerIntroduce = computed(() => {
  const obj = {
    csjsnd: `建于${record.value?.csjsnd.slice(0, 4)}年，`,
    nature: record.value?.nature ? `性质为${record.value?.nature}，` : '',
    block_number: record.value?.block_number ? `住宅共${handlerCount()}栋，由${handlerF1()}组成，` : '',
    usercount: `${record.value?.usercount}户${record.value?.before_number_pressurized ? `(其中加压户数：${record.value?.before_number_pressurized}户)，` : ''}`,
    pressurized_zone: `加压方式：${record.value?.pressurized_zone ?? '无'}。`
  }

  return obj.csjsnd + obj.nature + obj.block_number + obj.usercount + obj.pressurized_zone

  function handlerF1() {
    const str = record.value?.block_number
      .replace(/栋数：/g, '')
      .replace(/，/g, '')
      .split(';')
    return str
      ?.map((i) => i.split('：'))
      .map((i) => [i[0].replace('最高楼层', '（最高'), i[1] + '层）'].join(''))
      .join('、')
  }
})

function handlerCount() {
  const str = record.value?.block_number
  if (!str) return
  return str.split('，').reduce((total, item) => {
    let match = item.match(/栋数：(\d+)栋/)
    if (match) {
      let count = parseInt(match[1], 10)
      return total + count
    } else {
      return total
    }
  }, 0)
}
function handlerStr(str) {
  const length = str.length
  if (length == 2 || str == '[]' || !str) return '无'
  const data = JSON.parse(str)
  return Array.isArray(data) ? data.join('、') : data
}

// 判断小区状态
const have = computed(() => {
  switch (record.value.pump_room_status ?? '#') {
    case '无需二供改造':
      return [1]
    case '查漏补缺':
      return [1]
    case '已立项未进场':
      return [1]
    case '已立项施工中(指临供状态)':
      return [1, 2]
    case '已通水':
      return [1, 2, 3]
    default:
      if (record.value?.is_pump_room.includes('是') || record.value?.is_pump_room.includes('其它')) {
        if (record.value?.bfsl > 0) return [1, 2, 3]
        return [1]
      }
      return []
  }
})

// 二供
const textWater = computed(() => {
  if (!(record.value?.is_pump_room.includes('是') || record.value?.is_pump_room.includes('其它'))) return '无数据'
  let t1 = ''
  let t2 = ''
  if (have.value.includes(1)) {
    t1 = `改造前拥有 ${record.value.before_pump_room_count}（座）泵房，
    ${record.value.before_number_pumps ?? '(--)'}（台）${record.value.before_pump_material ?? '(--)'}材质水泵,
    ${record.value.before_pool_number ?? '(--)'}（座）${record.value.before_pool_material ?? '(--)'}材质水箱，
    泵房阀门以${record.value.bffmzycz ?? '(--)'}为主要材质。`
  }
  if (have.value.includes(3)) {
    t2 = `改造后拥有 ${record.value.bfsl}（座）泵房，${record.value.sbsl ?? '(--)'}（台）${record.value.sbcz ?? '(--)'}材质水泵,
    ${record.value.dxsc ?? '(--)'}（座）${record.value.dxscxcz ?? '(--)'}材质水箱，
    泵房阀门以${record.value.bffmzycz ?? '(--)'}为主要材质。
    `
  }

  return `${t1}${t2}`
})
</script>

<style lang="less" scoped>
.introduce {
  // min-height: 800rpx;
  padding: 22rpx 14rpx;
  line-height: 40rpx;
}
</style>
