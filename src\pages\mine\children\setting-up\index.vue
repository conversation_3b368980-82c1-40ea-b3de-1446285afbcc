<template>
  <div class="all fon-S52 setting-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
      <div class="decoration-wave wave-1"></div>
      <div class="decoration-wave wave-2"></div>
    </div>

    <div class="version-container">
      <!-- 应用信息区域 -->
      <div class="app-info">
        <div class="app-icon">
          <image src="/static/AppIcon.jpg" alt="应用图标" class="icon-img" />
        </div>
        <div class="app-title fon-W700">数据支持系统</div>
        <div class="app-subtitle fon-W600 c666">版本号：{{ appWgtVersion ?? appVersion }}</div>
      </div>

      <!-- 功能按钮区域 -->
      <div class="action-section">
        <div class="action-item" @click="openManual">
          <div class="action-icon manual-icon"></div>
          <div class="action-content">
            <div class="action-title">操作手册</div>
            <div class="action-desc">查看应用使用说明</div>
          </div>
          <div class="action-arrow">
            <wd-icon name="arrow-right" size="16px" color="#ccc" />
          </div>
        </div>

        <div class="action-item" @click="checkUpdate">
          <div class="action-icon update-icon"></div>
          <div class="action-content">
            <div class="action-title">检查更新</div>
            <div class="action-desc">检查是否有新版本</div>
          </div>
          <div class="action-arrow">
            <wd-icon name="arrow-right" size="16px" color="#ccc" />
          </div>
        </div>

        <div class="action-item" @click="changePassword">
          <div class="action-icon password-icon"></div>
          <div class="action-content">
            <div class="action-title">修改密码</div>
            <div class="action-desc">修改登录密码</div>
          </div>
          <div class="action-arrow">
            <wd-icon name="arrow-right" size="16px" color="#ccc" />
          </div>
        </div>

        <div class="action-item" @click="changeToPath">
          <div class="action-icon password-icon"></div>
          <div class="action-content">
            <div class="action-title">测试页面</div>
            <div class="action-desc">新功能开发测试</div>
          </div>
          <div class="action-arrow">
            <wd-icon name="arrow-right" size="16px" color="#ccc" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useCommonStore } from '/src/store/common'
import { storeToRefs } from 'pinia'

const { screenInformation } = storeToRefs(useCommonStore())
const { appWgtVersion, appVersion } = screenInformation.value

// 操作手册链接
const manualUrl = 'http://www.resource.szwgft.cn/oss/APPOperatingManual.docx'

// 打开操作手册
const openManual = () => {
  // #ifdef APP-PLUS
  // APP端使用系统浏览器打开
  try {
    plus.runtime.openURL(manualUrl)
    uni.showToast({
      title: '正在打开操作手册...',
      icon: 'none',
      duration: 2000
    })
  } catch (error) {
    console.error('打开链接失败:', error)
    // 如果打开失败，则复制链接
    uni.setClipboardData({
      data: manualUrl,
      success: () => {
        uni.showToast({
          title: '链接已复制，请在浏览器中打开',
          icon: 'success',
          duration: 3000
        })
      }
    })
  }
  // #endif

  // #ifdef H5
  // H5端使用window.open
  try {
    window.open(manualUrl, '_blank')
  } catch (error) {
    console.error('打开链接失败:', error)
    // 如果打开失败，则复制链接
    navigator.clipboard
      .writeText(manualUrl)
      .then(() => {
        uni.showToast({
          title: '链接已复制到剪贴板',
          icon: 'success'
        })
      })
      .catch(() => {
        uni.showModal({
          title: '操作手册链接',
          content: manualUrl,
          showCancel: false,
          confirmText: '确定'
        })
      })
  }
  // #endif

  // #ifdef MP
  // 小程序端复制链接到剪贴板
  uni.setClipboardData({
    data: manualUrl,
    success: () => {
      uni.showModal({
        title: '提示',
        content: '操作手册链接已复制到剪贴板，请在浏览器中打开查看',
        showCancel: false,
        confirmText: '确定'
      })
    },
    fail: () => {
      uni.showModal({
        title: '操作手册链接',
        content: manualUrl,
        showCancel: false,
        confirmText: '确定'
      })
    }
  })
  // #endif
}

// 检查更新
const checkUpdate = () => {
  uni.showToast({
    title: '当前已是最新版本',
    icon: 'success'
  })
}

// 修改密码
const changePassword = () => {
  uni.navigateTo({ url: '/src/pages/mine/children/setting-up/children/change-password' })
}

// 修改密码
const changeToPath = () => {
  uni.navigateTo({ url: '/src/pages/code/index' })
}
</script>

<style lang="less" scoped>
/* 页面主容器 */
.setting-page {
  min-height: 100%;
  position: relative;
  background: linear-gradient(135deg, #f0f6ff 0%, #e6f0f8 30%, #dce8f5 70%, #d6e4ff 100%);
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.12) 100%);
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.08);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  right: -50rpx;
  animation-delay: 0s;
}

.circle-2 {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  left: -30rpx;
  animation-delay: 2s;
}

.circle-3 {
  width: 120rpx;
  height: 120rpx;
  top: 30%;
  left: 20%;
  animation-delay: 4s;
}

.decoration-wave {
  position: absolute;
  width: 200%;
  height: 200rpx;
  background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.08) 25%, rgba(118, 75, 162, 0.12) 50%, rgba(102, 126, 234, 0.08) 75%, transparent 100%);
  transform: rotate(-15deg);
  animation: wave 8s ease-in-out infinite;
}

.wave-1 {
  top: 20%;
  left: -50%;
  animation-delay: 0s;
}

.wave-2 {
  top: 70%;
  left: -50%;
  animation-delay: 4s;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20rpx) scale(1.05);
    opacity: 1;
  }
}

@keyframes wave {
  0%,
  100% {
    transform: rotate(-15deg) translateX(-50rpx);
    opacity: 0.5;
  }
  50% {
    transform: rotate(-15deg) translateX(50rpx);
    opacity: 0.8;
  }
}

.version-container {
  position: relative;
  z-index: 2;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  padding: 40rpx 32rpx;
  box-sizing: border-box;
}

.app-info {
  text-align: center;
  padding: 80rpx 0 60rpx;
  position: relative;

  .app-icon {
    margin-bottom: 32rpx;
    position: relative;
    display: inline-block;

    .icon-img {
      width: 120rpx;
      height: 120rpx;
      border-radius: 24rpx;
      box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.2), 0 6rpx 20rpx rgba(118, 75, 162, 0.15), 0 2rpx 8rpx rgba(102, 126, 234, 0.1);
      transition: all 0.3s ease;
    }

    &::before {
      content: '';
      position: absolute;
      top: -8rpx;
      left: -8rpx;
      right: -8rpx;
      bottom: -8rpx;
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.12) 100%);
      border-radius: 32rpx;
      z-index: -1;
      opacity: 0;
      transition: all 0.3s ease;
    }

    &:hover::before {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  .app-title {
    font-size: 36rpx;
    color: #333;
    margin-bottom: 16rpx;
  }

  .app-subtitle {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 8rpx;
  }

  .version-info {
    font-size: 24rpx;
    color: #999;
  }
}

.action-section {
  flex: 1;

  .action-item {
    display: flex;
    align-items: center;
    padding: 32rpx 24rpx;
    margin-bottom: 16rpx;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15rpx);
    -webkit-backdrop-filter: blur(15rpx);
    border-radius: 20rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 12rpx 40rpx rgba(102, 126, 234, 0.12), 0 4rpx 20rpx rgba(118, 75, 162, 0.08), inset 0 1rpx 0 rgba(255, 255, 255, 0.4);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.1) 50%, transparent 100%);
      transition: all 0.6s ease;
    }

    &:active {
      transform: scale(0.98) translateY(2rpx);
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 4rpx 16rpx rgba(102, 126, 234, 0.12), 0 1rpx 8rpx rgba(118, 75, 162, 0.08);
    }

    &:hover::before {
      left: 0;
    }

    .action-icon {
      width: 80rpx;
      height: 80rpx;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 24rpx;
      position: relative;

      &::after {
        font-size: 32rpx;
        color: #fff;
      }

      &.manual-icon {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);

        &::after {
          content: '📖';
        }
      }

      &.update-icon {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

        &::after {
          content: '🔄';
        }
      }

      &.password-icon {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);

        &::after {
          content: '🔒';
        }
      }
    }

    .action-content {
      flex: 1;

      .action-title {
        font-size: 32rpx;
        color: #333;
        font-weight: 600;
        margin-bottom: 8rpx;
      }

      .action-desc {
        font-size: 24rpx;
        color: #999;
      }
    }

    .action-arrow {
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

.copyright {
  text-align: center;
  padding: 40rpx 0 20rpx;

  .copyright-text {
    font-size: 24rpx;
    color: #ccc;
    line-height: 1.5;
  }
}
</style>
