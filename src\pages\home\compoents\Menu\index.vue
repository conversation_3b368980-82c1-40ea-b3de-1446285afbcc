<template>
  <div class="menu-container">
    <!-- 简洁背景 -->
    <div class="menu-backdrop"></div>

    <!-- 拖拽指示器 -->
    <div class="drag-indicator"></div>

    <!-- 菜单内容 -->
    <div class="menu-content">
      <template v-for="(item, index) in list" :key="item.id">
        <div class="menu-item" :class="getItemClass(item)" :style="{ animationDelay: `${index * 0.08}s` }" @click="handlerClick(item)">
          <!-- 图标容器 -->
          <div class="icon-container" :class="getIconContainerClass(item)">
            <image class="item-icon" :src="item.icon" mode="aspectFit" />

            <!-- 状态指示器 -->
            <div class="status-dot" v-if="item.type === 'field'"></div>

            <!-- 新功能标签 -->
            <div class="new-badge" v-if="item.type === 'gis'">
              <text class="badge-text">NEW</text>
            </div>
          </div>

          <text class="item-name">{{ item.name }}</text>
        </div>
      </template>
    </div>

    <!-- 底部安全区域 -->
    <div class="safe-area-bottom"></div>
  </div>
</template>

<script setup>
const emit = defineEmits(['change'])

const list = [
  {
    type: 'field',
    id: 1,
    name: '总阀总表',
    path: '/src/pages/mine/children/field-work/map',
    icon: '/static/icons/valve-meter.svg'
  },
  {
    type: 'gis',
    id: 2,
    name: '日常维护',
    path: '/src/pages/mine/children/record/map',
    icon: '/static/icons/maintenance.svg'
  },
  {
    type: 'second',
    id: 3,
    name: '二供泵房',
    path: '/src/pages/mine/children/pump-house/pumpHouseMap',
    icon: '/static/icons/pump-house.svg'
  },
  {
    type: 'file',
    id: 4,
    name: '小区档案',
    path: '/src/pages/mine/children/material/map',
    icon: '/static/icons/archive.svg'
  }
]

function handlerClick(item) {
  emit('change', item)
}

// 获取菜单项样式类
function getItemClass(item) {
  return `menu-item-${item.type}`
}

// 获取图标容器类
function getIconContainerClass(item) {
  return `icon-container-${item.type}`
}
</script>

<style lang="less" scoped>
.menu-container {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  overflow: hidden;
}

/* 简洁背景 */
.menu-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-radius: 24rpx 24rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.08);
  z-index: 1;
}

/* 拖拽指示器 */
.drag-indicator {
  position: absolute;
  top: 12rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 48rpx;
  height: 4rpx;
  background: #e0e7ff;
  border-radius: 2rpx;
  z-index: 2;
}

.menu-content {
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 32rpx 24rpx 20rpx;
  z-index: 2;
}

.menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16rpx 12rpx;
  border-radius: 16rpx;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  animation: slideUp 0.5s ease-out forwards;
  opacity: 0;
  transform: translateY(20rpx);

  &:active {
    transform: scale(0.96);
    background: rgba(129, 199, 232, 0.08);
  }
}

@keyframes slideUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 图标容器 */
.icon-container {
  position: relative;
  width: 64rpx;
  height: 64rpx;
  border-radius: 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e3f2fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12rpx;
  transition: all 0.3s ease;
  overflow: visible;
}

.menu-item:active .icon-container {
  transform: scale(1.02);
  background: #f1f8ff;
  border-color: #81c7e8;
}

.icon-container-field {
  border-color: #22c55e;
  background: #f0fdf4;
}

.icon-container-gis {
  border-color: #3b82f6;
  background: #eff6ff;
}

.icon-container-second {
  border-color: #9333ea;
  background: #faf5ff;
}

.icon-container-file {
  border-color: #f97316;
  background: #fff7ed;
}

.item-icon {
  width: 48rpx;
  height: 48rpx;
  object-fit: contain;
}

/* 状态指示器 */
.status-dot {
  position: absolute;
  top: -4rpx;
  right: -4rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background: #22c55e;
  border: 2rpx solid #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(34, 197, 94, 0.3);
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

/* 新功能标签 */
.new-badge {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #3b82f6;
  color: #ffffff;
  font-size: 16rpx;
  font-weight: 600;
  padding: 2rpx 6rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ffffff;
  box-shadow: 0 2rpx 6rpx rgba(59, 130, 246, 0.3);
  z-index: 10;
}

.badge-text {
  font-size: 16rpx;
  font-weight: 600;
  letter-spacing: 0.2rpx;
}

/* 菜单项文字 */
.item-name {
  font-size: 24rpx;
  font-weight: 500;
  color: #4b5563;
  text-align: center;
  line-height: 1.3;
  max-width: 180rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.menu-item:active .item-name {
  color: #1f2937;
}

/* 底部安全区域 */
.safe-area-bottom {
  height: env(safe-area-inset-bottom, 20rpx);
  background: transparent;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .menu-backdrop {
    background: rgba(31, 41, 55, 0.95);
    box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.3);
  }

  .icon-container {
    background: #374151;
    border-color: #4b5563;
  }

  .icon-container-field {
    background: #064e3b;
    border-color: #059669;
  }

  .icon-container-gis {
    background: #1e3a8a;
    border-color: #3b82f6;
  }

  .icon-container-second {
    background: #581c87;
    border-color: #a855f7;
  }

  .icon-container-file {
    background: #9a3412;
    border-color: #f97316;
  }

  .item-name {
    color: #e5e7eb;
  }

  .menu-item:active .item-name {
    color: #f9fafb;
  }

  .drag-indicator {
    background: #6b7280;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .menu-content {
    padding: 28rpx 20rpx 16rpx;
  }

  .icon-container {
    width: 60rpx;
    height: 60rpx;
  }

  .item-icon {
    width: 44rpx;
    height: 44rpx;
  }

  .item-name {
    font-size: 22rpx;
    max-width: 76rpx;
  }

  .status-dot {
    width: 14rpx;
    height: 14rpx;
  }
}
</style>
