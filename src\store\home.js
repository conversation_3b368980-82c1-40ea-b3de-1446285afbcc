import { defineStore } from 'pinia'
import { ref } from 'vue'

export const userHomeStore = defineStore('homeStore', () => {
  const dots = ref([])
  function deliveryDots(val, type) {
    if (!val || val.length === 0) return (dots.value = [])
    dots.value = val.map((item, index) => settleData(item, index, type))
  }

  // 整理数据
  function settleData(value, index, type) {
    let coordinates
    if (type === 'gis') {
      value.icon = value.path4.length > 0 ? '已处理' : '未处理'
      value.path = `/src/pages/mine/children/record/detail?id=${value.id}`
      value.iconSize = 0.3
      coordinates = [value.x, value.y]
    } else if (type == 'field') {
      value.icon = value.point_Type
      value.path = `/src/pages/mine/children/field-work/detail?id=${value.id}`
      value.iconSize = 0.3
      coordinates = value.coordinates.split(',')
    } else {
      const state = value.ProgressStatus != '正常' ? '异常' : value.CurrentNode < 4 ? '临供前' : value.CurrentNode < 12 ? '临供' : value.CurrentNode < 14 ? '切换' : '初验'
      value.icon = state
      value.path = `/src/pages/mine/children/pump-house/pumpHouseMap?PumpRoomNumber=${value.PumpRoomNumber}`
      value.iconSize = 0.08
      coordinates = [value.X, value.Y]
    }
    return { type: 'Feature', id: index, properties: value, geometry: { type: 'Point', coordinates } }
  }

  return { dots, deliveryDots }
})

export default userHomeStore
