<template>
  <div class="demo-page">
    <div class="demo-header">
      <div class="header-content">
        <div class="title">SecondSource 组件演示</div>
        <div class="subtitle">优化后的列表卡片UI展示</div>
      </div>
    </div>
    
    <div class="demo-content">
      <SecondSource :code="demoCode" />
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import SecondSource from '../home/<USER>/ZoneContent/SecondSource.vue'

const demoCode = ref('DEMO001')
</script>

<style lang="less" scoped>
.demo-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.demo-header {
  padding: 60rpx 24rpx 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  .header-content {
    text-align: center;
    color: white;
  }
  
  .title {
    font-size: 36rpx;
    font-weight: 600;
    margin-bottom: 12rpx;
  }
  
  .subtitle {
    font-size: 24rpx;
    opacity: 0.9;
  }
}

.demo-content {
  padding: 24rpx 0;
}
</style>
