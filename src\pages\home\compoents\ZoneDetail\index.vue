<template>
  <div class="zone-detail">
    <!-- 详情信息区域 -->
    <div class="detail-list">
      <div class="detail-row">
        <div class="detail-item">
          <div class="label">所属水务</div>
          <div class="value">{{ detail.ManagerName ?? '--' }}</div>
        </div>
        <div class="detail-item">
          <div class="label">所属街道</div>
          <div class="value">{{ detail.Subdistrict ?? '--' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <div class="label">所属社区</div>
          <div class="value">{{ detail.Community ?? '--' }}</div>
        </div>
        <div class="detail-item">
          <div class="label">区块类别</div>
          <div class="value">{{ detail.Zone_Type ?? '--' }}</div>
        </div>
      </div>

      <!-- <div class="detail-row">
        <div class="detail-item">
          <div class="label">所属路段</div>
          <div class="value">{{ detail.Road ?? '--' }}</div>
        </div>
        <div class="detail-item">
          <div class="label">优饮批次</div>
          <div class="value">{{ detail.GoodDrinkBatch ?? '--' }}</div>
        </div>
      </div>

      <div class="detail-row">
        <div class="detail-item">
          <div class="label">优饮完成时间</div>
          <div class="value">{{ detail.GoodDrinkingFinishTime ?? '--' }}</div>
        </div>
        <div class="detail-item">
          <div class="label">二供批次</div>
          <div class="value">{{ detail.SecondarySupplylot ?? '--' }}</div>
        </div>
      </div> -->
    </div>

    <!-- 分段控制器区域 -->
    <div class="segmented-container">
      <scroll-view class="segmented-scroll" scroll-x="true" scroll-with-animation :scroll-left="scrollLeft" @touchstart.stop="" @touchmove.stop="" @touchend.stop="">
        <div class="segmented-wrapper">
          <wd-segmented :options="list" v-model:value="current" />
        </div>
      </scroll-view>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
const props = defineProps({ detail: Object, show: Number, list: Array })
const current = defineModel({ default: '简介' })
const height = computed(() => (props.show === 1 ? '0px' : '300rpx'))
const scrollLeft = computed(() => {
  if (current.value === '简介') return 0
  if (current.value === '档案') return 0
  if (current.value === '二供') return 45
  if (current.value === '总阀总表') return 97
  if (current.value === '日常维护') return 97
})
</script>

<style lang="less" scoped>
.zone-detail {
  .detail-list {
    height: v-bind(height);
    overflow: hidden;
    transition: all 0.5s ease-in-out;
    // padding: 16rpx 0;

    .detail-row {
      display: flex;
      margin-bottom: 24rpx;
      gap: 24rpx;

      &:last-child {
        margin-bottom: 0;
      }

      .detail-item {
        flex: 1;
        background: #f8f9fa;
        border-radius: 12rpx;
        padding: 20rpx 16rpx;
        border-left: 4rpx solid #4d63e0;
        transition: all 0.3s ease;

        &:hover {
          background: #f0f2ff;
          transform: translateY(-2rpx);
          box-shadow: 0 4rpx 12rpx rgba(77, 99, 224, 0.1);
        }

        .label {
          font-size: 24rpx;
          color: #666;
          margin-bottom: 8rpx;
          font-weight: 500;
          line-height: 1.4;
        }

        .value {
          font-size: 28rpx;
          color: #333;
          font-weight: 600;
          line-height: 1.5;
          word-break: break-all;

          &:empty::before {
            content: '--';
            color: #999;
            font-weight: 400;
          }
        }
      }
    }
  }

  .segmented-container {
    margin-top: 16rpx;

    .segmented-scroll {
      width: 100%;

      .segmented-wrapper {
        width: 130%;
        min-width: 100%;

        :deep(.wd-segmented) {
          background: #f8f9fa;
          border-radius: 12rpx;
          padding: 4rpx;

          .wd-segmented__item {
            border-radius: 8rpx;
            font-size: 26rpx;
            font-weight: 500;
            transition: all 0.3s ease;

            &.is-active {
              background: #4d63e0;
              color: #fff;
              box-shadow: 0 2rpx 8rpx rgba(77, 99, 224, 0.3);
            }

            &:not(.is-active) {
              color: #666;

              &:hover {
                background: rgba(77, 99, 224, 0.1);
                color: #4d63e0;
              }
            }
          }
        }
      }
    }
  }
}

// 响应式适配
@media (max-width: 750rpx) {
  .zone-detail {
    .detail-list {
      .detail-row {
        flex-direction: column;
        gap: 16rpx;

        .detail-item {
          .label {
            font-size: 22rpx;
          }

          .value {
            font-size: 26rpx;
          }
        }
      }
    }
  }
}
</style>
