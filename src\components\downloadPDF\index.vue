<template>
  <div class="downloadPDF"></div>
  <view :variation :change:variation="renderScript.watchVariationChange" />
</template>

<script>
export default {
  emits: ['markerClick', 'mapClick', 'mapLoad'],
  props: { variation: { type: Object, default: null } },
  data() {
    return {}
  },
  methods: {
    downPdf(path) {
      var fileName = new Date().valueOf() + '.pdf'
      let that = this
      this.base64ToFile(path, fileName, function (path1) {
        uni.showToast({
          title: `已保存在文件夹下！位置为：/storage/emulated/0/PDF存放处/${fileName}`,
          icon: 'none',
          duration: 2000,
          position: 'top'
        })
        setTimeout(() => {
          //自行控制是否打开文件

          //用第三方程序打开文件
          plus.runtime.openFile(`/storage/emulated/0/PDF存放处/${fileName}`, {}, function (error) {
            plus.nativeUI.toast(error.message)
          })
        }, 2000)
      })
    },
    base64ToFile(base64Str, fileName, callback) {
      //申请本地存储读写权限，创建文件夹
      plus.android.requestPermissions(
        ['android.permission.WRITE_EXTERNAL_STORAGE', 'android.permission.READ_EXTERNAL_STORAGE', 'android.permission.INTERNET', 'android.permission.ACCESS_WIFI_STATE'],
        (error) => {
          const File = plus.android.importClass('java.io.File')
          let file = new File('/storage/emulated/0/PDF存放处')
          if (!file.exists()) {
            //文件夹不存在即创建
            return file.mkdirs()
          }
          return false
        },
        (success) => {
          uni.showToast({
            title: '无法获取权限，文件下载将出错！',
            icon: 'none'
          })
        }
      )
      // 去除base64前缀,进行文件保存
      var index = base64Str.indexOf(',')
      var base64Data = base64Str.slice(index + 1, base64Str.length)
      let that = this
      plus.io.requestFileSystem(plus.io.PRIVATE_DOC, function (fs) {
        fs.root.getFile(
          fileName,
          {
            create: true
          },
          function (entry) {
            // 获得本地路径URL，file:///xxx/doc/1663062980631.xlsx
            var fullPath = '/storage/emulated/0/PDF存放处/' + fileName
            var FileOutputStream = plus.android.importClass('java.io.FileOutputStream')
            var out = new FileOutputStream(fullPath)

            try {
              // 智能选择写入方式，根据文件大小优化性能
              const fileSize = base64Data.length * 0.75 // 估算文件大小（base64比原文件大约33%）
              console.log('文件大小', fileSize / 1024 / 1024 + 'MB')

              if (fileSize < 5 * 1024 * 1024) {
                // 小于5MB使用直接写入
                that.writeBase64Direct(base64Data, out, () => {
                  // 回调
                  callback && callback()
                })
              } else {
                // 大文件使用优化的分块写入
                that.writeBase64InChunks(base64Data, out, () => {
                  // 回调
                  callback && callback()
                })
              }
            } catch (error) {
              console.error('文件写入失败:', error)
              out.close()
              uni.showToast({
                title: '文件保存失败！',
                icon: 'none'
              })
            }
          }
        )
      })
    },

    // 高性能分块写入base64数据
    writeBase64InChunks(base64Data, outputStream, callback) {
      const CHUNK_SIZE = 65536 // 64KB chunks，提高写入效率
      const BATCH_SIZE = 8 // 每批处理8个chunk，减少setTimeout调用
      let offset = 0
      let hasError = false

      // 使用BufferedOutputStream提高写入性能
      const BufferedOutputStream = plus.android.importClass('java.io.BufferedOutputStream')
      const bufferedStream = new BufferedOutputStream(outputStream, 65536) // 64KB缓冲区

      const writeBatch = () => {
        if (hasError) return

        if (offset >= base64Data.length) {
          try {
            bufferedStream.flush() // 确保所有数据写入
            bufferedStream.close()
            callback()
          } catch (error) {
            console.error('文件关闭失败:', error)
          }
          return
        }

        try {
          // 批量处理多个chunk
          for (let i = 0; i < BATCH_SIZE && offset < base64Data.length; i++) {
            const remainingLength = base64Data.length - offset
            const chunkSize = Math.min(CHUNK_SIZE, remainingLength)
            let chunk = base64Data.slice(offset, offset + chunkSize)

            // 确保chunk长度是4的倍数（base64特性）
            if (offset + chunkSize < base64Data.length && chunk.length % 4 !== 0) {
              const adjustment = 4 - (chunk.length % 4)
              chunk = base64Data.slice(offset, offset + chunkSize + adjustment)
            }

            const bytes = this.base64ChunkToByteArrayOptimized(chunk)
            if (bytes.length > 0) {
              bufferedStream.write(bytes)
              offset += chunk.length
            } else {
              hasError = true
              console.error('Base64解码失败，chunk为空')
              break
            }
          }

          // 减少setTimeout调用频率，提高性能
          if (offset < base64Data.length) {
            setTimeout(writeBatch, 1) // 最小延迟
          } else {
            writeBatch() // 直接调用完成
          }
        } catch (error) {
          hasError = true
          console.error('批量写入失败:', error)
          try {
            bufferedStream.close()
          } catch (closeError) {
            console.error('流关闭失败:', closeError)
          }
        }
      }

      writeBatch()
    },

    // 备用方案：直接写入（适用于中等大小文件）
    writeBase64Direct(base64Data, outputStream, callback) {
      try {
        // 使用BufferedOutputStream提高性能
        const BufferedOutputStream = plus.android.importClass('java.io.BufferedOutputStream')
        const bufferedStream = new BufferedOutputStream(outputStream, 131072) // 128KB缓冲区

        // 分段解码，避免内存问题
        const DECODE_CHUNK_SIZE = 1048576 // 1MB解码块
        let offset = 0

        while (offset < base64Data.length) {
          const chunkSize = Math.min(DECODE_CHUNK_SIZE, base64Data.length - offset)
          let chunk = base64Data.slice(offset, offset + chunkSize)

          // 确保chunk是有效的base64
          while (chunk.length % 4 !== 0) {
            chunk += '='
          }

          const bytes = this.base64ChunkToByteArrayOptimized(chunk)
          bufferedStream.write(bytes)
          offset += chunkSize
        }

        bufferedStream.flush()
        bufferedStream.close()
        callback()
      } catch (error) {
        console.error('直接写入失败:', error)
        // 降级到分块写入
        this.writeBase64InChunks(base64Data, outputStream, callback)
      }
    },

    // 高性能优化的base64转字节数组方法
    base64ChunkToByteArrayOptimized(chunk) {
      try {
        // 处理padding，确保是有效的base64字符串
        while (chunk.length % 4 !== 0) {
          chunk += '='
        }

        const binaryString = atob(chunk)
        const length = binaryString.length

        // 使用TypedArray提高性能
        const uint8Array = new Uint8Array(length)
        for (let i = 0; i < length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i)
        }

        // 转换为Java字节数组格式
        const javaBytes = []
        for (let i = 0; i < length; i++) {
          const byte = uint8Array[i]
          javaBytes[i] = byte >= 128 ? byte - 256 : byte
        }

        return javaBytes
      } catch (error) {
        console.error('优化Base64解码失败:', error, 'chunk length:', chunk.length)
        return []
      }
    },

    // 保留原方法作为备用
    base64ChunkToByteArray(chunk) {
      try {
        // 处理padding，确保是有效的base64字符串
        while (chunk.length % 4 !== 0) {
          chunk += '='
        }

        const binaryString = atob(chunk)
        const bytes = []

        for (let i = 0; i < binaryString.length; i++) {
          const byte = binaryString.charCodeAt(i)
          // 转换为有符号字节（Java byte范围：-128到127）
          bytes.push(byte >= 128 ? byte - 256 : byte)
        }

        return bytes
      } catch (error) {
        console.error('Base64解码失败:', error, 'chunk length:', chunk.length)
        return []
      }
    },

    // 保留原方法作为备用方案（适用于小文件）
    base64ToByteArray(base64Str) {
      try {
        const binaryString = atob(base64Str)
        const uint8Array = new Uint8Array(binaryString.length)

        for (let i = 0; i < binaryString.length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i)
        }
        let arr = []
        Array.from(uint8Array).map((num) => {
          arr.push(num >= 128 ? num - 256 : num)
        })
        return arr
      } catch (error) {
        console.error('传统base64转换失败:', error)
        return []
      }
    },
    showLoading() {
      uni.showLoading({ title: '正在导出pdf文件...', mask: true })
    },
    hideLoading() {
      uni.hideLoading()
    }
  }
}
</script>

<script lang="renderjs" module="renderScript">
import html2Canvas from 'html2canvas'
import JsPDF from 'jspdf'
export default {
  methods: {
    watchVariationChange({type, value}) {
      if(type && value) this[type](value)
    },
    // 页面导出pdf
    download(id){
			this.$ownerInstance.callMethod('showLoading'); //显示加载
      try {
        const detail = document.querySelector(id);
        html2Canvas(detail, {
          allowTaint: true,
          useCORS: true,
          scale: 1.4, // 提高分辨率
          windowHeight: detail.scrollHeight // 确保捕获完整高度
        }).then((canvas) => {
          return new Promise((resolve) => {
            setTimeout(() => resolve(canvas), 500)
          }).then((canvas) => {
            const contentWidth = canvas.width;
            const contentHeight = canvas.height;

            // 创建自定义尺寸的PDF（核心修改）
            const pdf = new JsPDF({
              orientation: contentWidth > contentHeight ? 'l' : 'p', // 自动方向
              unit: 'px',
              format: [contentWidth, contentHeight] // 完全匹配内容尺寸
            });

            // 直接添加完整内容（移除分页逻辑）
            pdf.addImage(canvas, 'PNG', 0, 0, contentWidth, contentHeight);

            const blob = pdf.output("datauristring");
            this.$ownerInstance.callMethod('downPdf', blob);
          }).catch((r) => {
            console.log(r);
            this.$ownerInstance.callMethod('hideLoading');
          })
        });

      } catch (error) {
        this.$ownerInstance.callMethod('hideLoading');
      }
    }
  },
}
</script>
