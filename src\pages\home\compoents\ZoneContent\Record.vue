<template>
  <div class="record-list">
    <template v-for="item in list" :key="item.id">
      <div @click="handleClick(item)" class="record-card mar-B16 pad-24 back-white box-shadow border-R10" :class="getStatusClass(item)">
        <!-- 卡片头部 -->
        <div class="card-header f-between">
          <div class="facility-info f-y-center">
            <div class="facility-icon f-xy-center mar-R12"></div>
            <div>
              <div class="facility-title fon-S28 fon-W600 color-primary">{{ item.subdistric }}</div>
              <div class="task-type fon-S22 color-999 mar-T4">{{ item.task_Type }}</div>
            </div>
          </div>
          <div class="status-info text-right">
            <div class="status-tag" :class="getStatusTagClass(item)">{{ getStatusText(item) }}</div>
            <div class="date-info fon-S22 color-666 mar-T8">{{ item.inputdate.slice(0, 10) }}</div>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content mar-T16">
          <div class="remark-section" v-if="getRemark(item)">
            <div class="remark-label fon-S20 color-999 mar-B4">备注信息</div>
            <div class="remark-text fon-S24 color-666 text-nowrap-2">{{ getRemark(item) }}</div>
          </div>
          <div class="staff-info f-between mar-T12">
            <div class="staff-item">
              <div class="staff-label fon-S20 color-999">上报人员</div>
              <div class="staff-name fon-S24 color-333 fon-W500">{{ item.inputstaff || '未知' }}</div>
            </div>
            <div class="arrow-icon">
              <wd-icon name="arrow-right" color="#ccc" size="18px"></wd-icon>
            </div>
          </div>
        </div>
      </div>
    </template>

    <div v-if="list.length === 0" class="all back-white f-xy-center">
      <wd-status-tip image="search" tip="无数据" />
    </div>
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { handlerImage } from '/src/utils/index.js'
import { getOperationFeedbackZoneCodelApi } from '/src/services/model/submit.js'
import uniUtil from '/src/utils/uniUtil.js'
import { userHomeStore } from '/src/store/home.js'
const { deliveryDots } = userHomeStore()

const props = defineProps({
  code: String,
  current: String
})

watch(() => props.code, getList, { immediate: true })

const list = ref([])
watch(list, (val) => deliveryDots(val, 'gis'))

async function getList(zoneCode) {
  try {
    const result = await getOperationFeedbackZoneCodelApi(zoneCode)
    list.value = result.data.operationFeedbacks
  } catch (error) {
    list.value = []
  }
}

function handleClick(value) {
  uniUtil.navigateTo(`/src/pages/mine/children/record/detail?id=${value.id}`)
}

// 获取备注信息
function getRemark(item) {
  return props.current === '已处理' ? item.remark2 : item.remark1
}

// 获取状态样式类
function getStatusClass(item) {
  // 根据是否有remark2判断是否已处理
  const isFinished = item.remark2 && item.remark2.trim()
  return isFinished ? 'status-finished' : 'status-pending'
}

// 获取状态标签样式类
function getStatusTagClass(item) {
  const isFinished = item.remark2 && item.remark2.trim()
  return isFinished ? 'tag-finished' : 'tag-pending'
}

// 获取状态文本
function getStatusText(item) {
  const isFinished = item.remark2 && item.remark2.trim()
  return isFinished ? '已处理' : '待处理'
}
</script>

<style lang="less" scoped>
// 记录卡片样式
.record-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
  }

  // 状态左边框
  &.status-finished {
    border-left: 6rpx solid #52c41a;
  }

  &.status-pending {
    border-left: 6rpx solid #faad14;
  }
}

// 卡片头部
.card-header {
  align-items: flex-start;
}

// 设施信息
.facility-info {
  flex: 1;
}

.facility-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
  box-shadow: 0 2rpx 8rpx rgba(77, 99, 224, 0.3);
}

.facility-title {
  color: #4d63e0;
  line-height: 1.2;
}

.task-type {
  line-height: 1.2;
}

// 状态信息
.status-info {
  align-items: flex-end;
}

.status-tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 500;
  line-height: 1;
  display: inline-block;

  &.tag-finished {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    color: #52c41a;
    border: 1rpx solid #b7eb8f;
  }

  &.tag-pending {
    background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
    color: #faad14;
    border: 1rpx solid #ffe58f;
  }
}

.date-info {
  line-height: 1.2;
}

// 卡片内容
.card-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

// 备注区域
.remark-section {
  background: rgba(77, 99, 224, 0.04);
  padding: 12rpx 16rpx;
  border-radius: 8rpx;
  border-left: 3rpx solid #4d63e0;
}

.remark-label {
  line-height: 1.2;
}

.remark-text {
  line-height: 1.4;
}

// 员工信息
.staff-info {
  align-items: center;
}

.staff-item {
  flex: 1;
}

.staff-label {
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.staff-name {
  line-height: 1.2;
}

.arrow-icon {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.record-card:active .arrow-icon {
  opacity: 1;
  transform: translateX(4rpx);
}

// 颜色类
.color-primary {
  color: #4d63e0;
}

.color-333 {
  color: #333;
}

.color-666 {
  color: #666;
}

.color-999 {
  color: #999;
}

// 响应式优化
@media (max-width: 750rpx) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .status-info {
    align-items: flex-start;
  }
}
</style>
