<template>
  <div class="field-work">
    <template v-for="item in list" :key="item.id">
      <div @click="handleClick(item)" class="field-work-card mar-B16 pad-24 back-white box-shadow border-R10" :class="getPointTypeClass(item.point_Type)">
        <!-- 卡片头部 -->
        <div class="card-header f-between">
          <div class="point-info f-y-center">
            <div class="point-icon f-xy-center mar-R16">
              <wd-icon :name="getPointTypeIcon(item.point_Type)" size="18px" color="#fff"></wd-icon>
            </div>
            <div>
              <div class="zone-title fon-S32 fon-W700 color-primary">{{ item.zone_Name }}</div>
              <div class="point-subtitle fon-S22 color-666 mar-T4">{{ getPointTypeDescription(item.point_Type) }}</div>
            </div>
          </div>
          <div class="time-badge" :class="getTimeBadgeClass(item.created_Time)">
            <div class="time-text fon-S20 fon-W600">{{ getRelativeTime(item.created_Time) }}</div>
            <div class="date-text fon-S18 color-666">{{ formatFullDate(item.created_Time) }}</div>
          </div>
        </div>

        <!-- 卡片内容 -->
        <div class="card-content mar-T20">
          <div class="info-grid">
            <div class="info-item">
              <div class="info-icon f-xy-center">
                <wd-icon name="location" size="14px" color="#52c41a"></wd-icon>
              </div>
              <div class="info-details">
                <div class="info-label fon-S20 color-999">地址信息</div>
                <div class="info-value fon-S24 color-333 fon-W500">{{ item.address }}</div>
              </div>
            </div>

            <div class="info-item">
              <div class="info-icon f-xy-center">
                <wd-icon name="setting" size="14px" color="#4d63e0"></wd-icon>
              </div>
              <div class="info-details">
                <div class="info-label fon-S20 color-999">点位类型</div>
                <div class="info-value fon-S24 color-333 fon-W500">{{ item.point_Type }}</div>
              </div>
            </div>
          </div>

          <!-- 状态信息 -->
          <div class="status-section mar-T16">
            <div class="status-item">
              <div class="status-icon f-xy-center">
                <wd-icon name="time" size="12px" color="#fa8c16"></wd-icon>
              </div>
              <div class="status-text fon-S20 color-666">点击查看详细现场信息</div>
            </div>
          </div>

          <!-- 底部操作区 -->
          <div class="action-area f-between mar-T16">
            <div class="action-tags">
              <div class="action-tag" :class="getPriorityClass(item.point_Type)">
                {{ getPriorityText(item.point_Type) }}
              </div>
            </div>
            <div class="arrow-icon">
              <wd-icon name="arrow-right" color="#ccc" size="18px"></wd-icon>
            </div>
          </div>
        </div>
      </div>
    </template>

    <div v-if="list.length === 0" class="all back-white border-R12 f-xy-center">
      <wd-status-tip image="search" tip="无数据" />
    </div>
  </div>
  <div style="padding: 80rpx"></div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue'
import { getalveVerificationListApi } from '/src/services/model/submit.js'
import uniUtil from '/src/utils/uniUtil.js'
import { userHomeStore } from '/src/store/home.js'
const { deliveryDots } = userHomeStore()

const props = defineProps({ code: String })

watch(() => props.code, getList, { immediate: true })

const list = ref([])
watch(list, (val) => deliveryDots(val, 'field'))

async function getList(code) {
  if (!code) return
  try {
    const { data } = await getalveVerificationListApi(code)
    list.value = data.valveVerification
  } catch (error) {
    list.value = []
  }
}
function handlerImage(type) {
  const url = type === '总表' ? 'https://www.szwgft.cn:8090/AppIcon/50cea3d9-96c7-43ce-8b70-619d8996964a.png' : 'https://www.szwgft.cn:8090/AppIcon/58b25c06-034c-4066-87d4-0e20135ee5fc.png'
  return url
}

function handleClick(val) {
  uniUtil.navigateTo(`/src/pages/mine/children/field-work/detail?id=${val.id}`)
}

// 获取点位类型图标
function getPointTypeIcon(pointType) {
  const iconMap = {
    总阀: 'setting',
    总表: 'chart',
    阀门: 'setting',
    水表: 'chart-pie',
    管道: 'link',
    泵房: 'home',
    设备: 'tool',
    监测点: 'location'
  }
  return iconMap[pointType] || 'location'
}

// 获取点位类型样式类
function getPointTypeClass(pointType) {
  const classMap = {
    总阀: 'point-valve',
    总表: 'point-meter',
    阀门: 'point-valve',
    水表: 'point-meter',
    管道: 'point-pipe',
    泵房: 'point-pump',
    设备: 'point-device',
    监测点: 'point-monitor'
  }
  return classMap[pointType] || 'point-default'
}

// 获取点位类型描述
function getPointTypeDescription(pointType) {
  const descMap = {
    总阀: '主要供水控制阀门',
    总表: '区域总水表监测',
    阀门: '供水管道控制阀',
    水表: '用户水量计量表',
    管道: '供水管网设施',
    泵房: '供水加压设备',
    设备: '供水相关设备',
    监测点: '水质监测点位'
  }
  return descMap[pointType] || '现场作业点位'
}

// 获取时间徽章样式类
function getTimeBadgeClass(createdTime) {
  const now = new Date()
  const created = new Date(createdTime)
  const diffDays = Math.floor((now - created) / (1000 * 60 * 60 * 24))

  // 今天
  if (diffDays === 0) return 'time-today'

  // 最近3天
  if (diffDays <= 3) return 'time-recent'

  // 本周内
  if (diffDays <= 7) return 'time-week'

  // 本月内
  if (diffDays <= 30) return 'time-month'

  // 本年内
  if (diffDays <= 365) return 'time-year'

  // 超过一年
  return 'time-old'
}

// 获取相对时间
function getRelativeTime(createdTime) {
  const now = new Date()
  const created = new Date(createdTime)
  const diffMs = now - created
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffMinutes = Math.floor(diffMs / (1000 * 60))

  // 当天的时间显示
  if (diffDays === 0) {
    if (diffMinutes < 5) return '刚刚'
    if (diffMinutes < 60) return `${diffMinutes}分钟前`
    return `${diffHours}小时前`
  }

  // 昨天
  if (diffDays === 1) return '昨天'

  // 本周内
  if (diffDays <= 7) return `${diffDays}天前`

  // 本月内
  if (diffDays <= 30) return `${Math.floor(diffDays / 7)}周前`

  // 本年内
  const diffMonths = Math.floor(diffDays / 30)
  if (diffMonths < 12) return `${diffMonths}个月前`

  // 超过一年
  const diffYears = Math.floor(diffDays / 365)
  return `${diffYears}年前`
}

// 格式化完整日期
function formatFullDate(dateTime) {
  if (!dateTime) return '--'

  const date = new Date(dateTime)
  const now = new Date()
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  // 如果是今年，不显示年份
  if (year === now.getFullYear()) {
    return `${month}/${day} ${hours}:${minutes}`
  }

  // 如果不是今年，显示完整年份
  return `${year}/${month}/${day} ${hours}:${minutes}`
}

// 获取优先级样式类
function getPriorityClass(pointType) {
  const classMap = {
    总阀: 'priority-high',
    总表: 'priority-high',
    泵房: 'priority-high',
    阀门: 'priority-medium',
    水表: 'priority-medium',
    设备: 'priority-medium',
    管道: 'priority-normal',
    监测点: 'priority-normal'
  }
  return classMap[pointType] || 'priority-normal'
}

// 获取优先级文本
function getPriorityText(pointType) {
  const textMap = {
    总阀: '重要设施',
    总表: '重要设施',
    泵房: '核心设备',
    阀门: '控制设施',
    水表: '计量设备',
    设备: '辅助设备',
    管道: '基础设施',
    监测点: '监测设施'
  }
  return textMap[pointType] || '一般设施'
}
</script>

<style lang="less" scoped>
.field-work {
  padding: 16rpx;
}

// 现场作业卡片样式
.field-work-card {
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20rpx;
  box-shadow: 0 6rpx 30rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.12);
  }

  // 不同点位类型的左边框颜色
  &.point-valve {
    border-left: 6rpx solid #ff4d4f;
  }

  &.point-meter {
    border-left: 6rpx solid #1890ff;
  }

  &.point-pipe {
    border-left: 6rpx solid #52c41a;
  }

  &.point-pump {
    border-left: 6rpx solid #722ed1;
  }

  &.point-device {
    border-left: 6rpx solid #fa8c16;
  }

  &.point-monitor {
    border-left: 6rpx solid #13c2c2;
  }

  &.point-default {
    border-left: 6rpx solid #32cbdb;
  }
}

// 卡片头部
.card-header {
  align-items: flex-start;
}

// 点位信息
.point-info {
  flex: 1;
}

.point-icon {
  width: 56rpx;
  height: 56rpx;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #4d63e0 0%, #6366f1 100%);
  box-shadow: 0 4rpx 16rpx rgba(77, 99, 224, 0.3);
}

.zone-title {
  color: #32cbdb;
  line-height: 1.2;
}

.point-subtitle {
  line-height: 1.3;
  opacity: 0.8;
}

// 时间徽章
.time-badge {
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  text-align: center;
  min-width: 80rpx;

  &.time-today {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    border: 1rpx solid #b7eb8f;

    .time-text {
      color: #52c41a;
    }
  }

  &.time-recent {
    background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
    border: 1rpx solid #91d5ff;

    .time-text {
      color: #1890ff;
    }
  }

  &.time-week {
    background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
    border: 1rpx solid #ffcc02;

    .time-text {
      color: #fa8c16;
    }
  }

  &.time-month {
    background: linear-gradient(135deg, #f9f0ff 0%, #efdbff 100%);
    border: 1rpx solid #d3adf7;

    .time-text {
      color: #722ed1;
    }
  }

  &.time-year {
    background: linear-gradient(135deg, #fff0f6 0%, #ffd6e7 100%);
    border: 1rpx solid #ffadd2;

    .time-text {
      color: #eb2f96;
    }
  }

  &.time-old {
    background: linear-gradient(135deg, #f5f5f5 0%, #d9d9d9 100%);
    border: 1rpx solid #d9d9d9;

    .time-text {
      color: #666;
    }
  }
}

.date-text {
  line-height: 1.2;
  margin-top: 2rpx;
}

// 卡片内容
.card-content {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

// 信息网格
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.info-icon {
  width: 32rpx;
  height: 32rpx;
  border-radius: 8rpx;
  background: rgba(77, 99, 224, 0.1);
}

.info-details {
  flex: 1;
}

.info-label {
  line-height: 1.2;
  margin-bottom: 4rpx;
}

.info-value {
  line-height: 1.2;
}

// 状态区域
.status-section {
  background: rgba(50, 203, 219, 0.04);
  padding: 12rpx 16rpx;
  border-radius: 12rpx;
  border-left: 3rpx solid #32cbdb;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-icon {
  width: 24rpx;
  height: 24rpx;
  border-radius: 6rpx;
  background: rgba(250, 140, 22, 0.1);
}

.status-text {
  line-height: 1.3;
}

// 操作区域
.action-area {
  align-items: center;
}

.action-tags {
  display: flex;
  gap: 8rpx;
}

.action-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 500;

  &.priority-high {
    background: linear-gradient(135deg, #fff2e8 0%, #ffd591 100%);
    color: #fa541c;
    border: 1rpx solid #ffbb96;
  }

  &.priority-medium {
    background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
    color: #52c41a;
    border: 1rpx solid #b7eb8f;
  }

  &.priority-normal {
    background: linear-gradient(135deg, #f0f0f0 0%, #d9d9d9 100%);
    color: #666;
    border: 1rpx solid #d9d9d9;
  }
}

.arrow-icon {
  opacity: 0.6;
  transition: all 0.3s ease;
}

.field-work-card:active .arrow-icon {
  opacity: 1;
  transform: translateX(4rpx);
}

// 颜色类
.color-primary {
  color: #32cbdb;
}

.color-333 {
  color: #333;
}

.color-666 {
  color: #666;
}

.color-999 {
  color: #999;
}

// 动画效果
.field-work-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12rpx;
  }

  .info-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .point-icon {
    width: 48rpx;
    height: 48rpx;
  }

  .time-badge {
    align-self: flex-end;
  }
}
</style>
